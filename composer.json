{"name": "guncha25/mb8", "description": "Mezabirza D10 project", "type": "project", "license": "GPL-2.0-or-later", "authors": [{"name": "<PERSON><PERSON>", "role": "Developer"}], "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}, {"type": "composer", "url": "https://asset-packagist.org"}, {"type": "package", "package": {"name": "furf/j<PERSON>y-ui-touch-punch", "version": "dev-master", "type": "drupal-library", "dist": {"url": "https://github.com/furf/jquery-ui-touch-punch/archive/master.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "feimosi/baguettebox.js", "version": "1.11.1", "type": "drupal-library", "dist": {"url": "https://github.com/feimosi/baguetteBox.js/archive/refs/tags/v1.11.1.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "kenwheeler/slick", "version": "1.8.0", "type": "drupal-library", "dist": {"url": "https://github.com/kenwheeler/slick/archive/refs/tags/1.8.0.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "afarkas/lazysizes", "version": "5.3.1", "type": "drupal-library", "dist": {"url": "https://github.com/aFarkas/lazysizes/archive/refs/tags/5.3.1.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "davatron5000/fitvids", "version": "1.2.0", "type": "drupal-library", "dist": {"url": "https://github.com/davatron5000/FitVids.js/archive/refs/tags/v1.2.0.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "jere<PERSON><PERSON><PERSON><PERSON>/photo-sphere-viewer", "version": "2.9.0", "type": "drupal-library", "dist": {"url": "https://github.com/JeremyHeleine/Photo-Sphere-Viewer/archive/refs/tags/v2.9.zip", "type": "zip"}}}], "require": {"php": ">=8.3", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-zip": "*", "afarkas/lazysizes": "5.3.1", "composer/installers": "^1.9", "cweagans/composer-patches": "^1.7", "davatron5000/fitvids": "^1.2", "drupal/action": "^0.2", "drupal/admin_toolbar": "^3.5", "drupal/allowed_text_format_field_widget": "^1.0", "drupal/baguettebox": "2.0.x-dev@dev", "drupal/better_exposed_filters": "^7.0", "drupal/blazy": "^3.0", "drupal/charts": "^5.1", "drupal/ckeditor5_embedded_content": "1.0.x-dev@dev", "drupal/commerce": "^3.0", "drupal/commerce_invoice": "^2.0@beta", "drupal/config_split": "^2.0", "drupal/core-composer-scaffold": "^10.1", "drupal/core-recommended": "^10.1", "drupal/cshs": "^4.0", "drupal/csv_serialization": "^4.0", "drupal/datalayer": "^2.1", "drupal/date_popup": "^2.0", "drupal/datetime_flatpickr": "^3.0", "drupal/datetimehideseconds": "^1.0", "drupal/domain": "^2.0@beta", "drupal/easy_breadcrumb": "^2.0", "drupal/editor_advanced_link": "^2.0", "drupal/editor_file": "^2.0@RC", "drupal/elasticsearch_helper": "^8.1", "drupal/elasticsearch_helper_views": "^8.0", "drupal/emulsify_twig": "^5.0", "drupal/entity_print": "^2.14", "drupal/eu_cookie_compliance": "dev-1.x#7b24a6383fce6", "drupal/facebook_pixel": "^2.0@RC", "drupal/field_group": "^3.1", "drupal/fitvids": "^2.0", "drupal/form_options_attributes": "^2.0", "drupal/formtips": "^1.5", "drupal/geofield": "^1.15", "drupal/geofield_map": "^11.0", "drupal/gin": "^4.0.6", "drupal/gin_toolbar": "^2.0", "drupal/google_api_client": "^4.3", "drupal/google_place_autocomplete": "^2.1", "drupal/google_tag": "^2.0", "drupal/googlelogin-googlelogin": "^9.2", "drupal/image_field_360": "2.x-dev", "drupal/imageapi_optimize": "^4.0", "drupal/imageapi_optimize_tinypng": "^1.2@beta", "drupal/inline_entity_form": "^3.0@RC", "drupal/inline_responsive_images": "3.x-dev@dev", "drupal/inqube": "^2.1", "drupal/jquery_ui": "^1.7", "drupal/jquery_ui_datepicker": "^2.1", "drupal/jquery_ui_slider": "^2.1", "drupal/jquery_ui_touch_punch": "^1.1", "drupal/leaflet": "^10.2", "drupal/mail_login": "^4.0", "drupal/masquerade": "^2.0@beta", "drupal/memcache": "^2.3", "drupal/message": "^1.5", "drupal/metatag": "^2.0", "drupal/ms_clarity": "^2.0@beta", "drupal/nocurrent_pass": "^2.0", "drupal/paragraphs": "^1.12", "drupal/paragraphs_asymmetric_translation_widgets": "^1.2", "drupal/pathauto": "^1.8", "drupal/poll": "^1.6", "drupal/private_files_download_permission": "^3.1", "drupal/queue_mail": "^1.6", "drupal/quick_node_clone": "^1.16", "drupal/rabbit_hole": "^2.0@alpha", "drupal/rate": "^3.1", "drupal/redirect": "^1.6", "drupal/require_on_publish": "^1.6", "drupal/schema_metatag": "^3.0", "drupal/shs": "^2.0@RC", "drupal/simple_pass_reset": "^1.1", "drupal/simple_recaptcha": "^1.0-beta", "drupal/simple_sitemap": "^4.0", "drupal/simplei": "^3.0", "drupal/simplenews": "^4.0", "drupal/slick": "^3.0", "drupal/slick_paragraphs": "^3.0", "drupal/smart_trim": "^2.1", "drupal/symfony_mailer": "^1.2", "drupal/taxonomy_access_fix": "^4.0", "drupal/url_embed": "^3.0@beta", "drupal/vbo_export": "^4.1", "drupal/views_aggregator": "^2.0", "drupal/views_ajax_history": "^1.7", "drupal/views_data_export": "^1.1", "drupal/views_infinite_scroll": "^2.0", "drupal/viewsreference": "^2.0-beta", "drupal/votingapi": "^3.0@beta", "drush/drush": "^13.0", "elasticsearch/elasticsearch": "^8.0", "feimosi/baguettebox.js": "1.11.1", "furf/jquery-ui-touch-punch": "dev-master", "google/apiclient": "v2.18.0", "jeremyheleine/photo-sphere-viewer": "^2.9", "kenwheeler/slick": "1.8.0", "klix/klix-sdk-php": "^3.1", "kwn/number-to-words": "^2.6", "maennchen/zipstream-php": "^2.1", "mikehaertl/phpwkhtmltopdf": "~2.1", "npm-asset/chart.js": "^4.4", "npm-asset/chartjs-adapter-date-fns": "^3.0", "npm-asset/chartjs-plugin-datalabels": "^2.0", "oomphinc/composer-installers-extender": "^2.0", "phpspec/prophecy-phpunit": "^2.0", "pusher/pusher-php-server": "^7.0", "symfony/phpunit-bridge": "^6.3", "tecnickcom/tcpdf": "~6", "vlucas/phpdotenv": "^5.2", "webflo/drupal-finder": "^1.2"}, "require-dev": {"drupal/stage_file_proxy": "^3.1", "wunderio/code-quality": "^3.0", "zaporylie/composer-drupal-optimizations": "^1.1"}, "conflict": {"drupal/drupal": "*"}, "minimum-stability": "stable", "prefer-stable": true, "config": {"process-timeout": 6000, "sort-packages": true, "discard-changes": true, "allow-plugins": {"composer/installers": true, "cweagans/composer-patches": true, "dealerdirect/phpcodesniffer-composer-installer": true, "drupal/console-extend-plugin": true, "drupal/core-composer-scaffold": true, "oomphinc/composer-installers-extender": true, "php-http/discovery": true, "phpro/grumphp": true, "phpstan/extension-installer": true, "zaporylie/composer-drupal-optimizations": true}}, "autoload": {"classmap": ["scripts/composer/ScriptHandler.php"], "files": ["load.environment.php"]}, "scripts": {"drupal-scaffold": "DrupalComposer\\DrupalScaffold\\Plugin::scaffold", "pre-install-cmd": ["DrupalProject\\composer\\ScriptHandler::checkComposerVersion"], "pre-update-cmd": ["DrupalProject\\composer\\ScriptHandler::checkComposerVersion"], "post-install-cmd": ["DrupalProject\\composer\\ScriptHandler::createRequiredFiles"], "post-update-cmd": ["DrupalProject\\composer\\ScriptHandler::createRequiredFiles"]}, "extra": {"installer-types": ["npm-asset"], "drupal-scaffold": {"locations": {"web-root": "web/"}, "file-mapping": {"[web-root]/sites/development.services.yml": false}}, "installer-paths": {"web/libraries/chart.js": ["npm-asset/chart.js"], "web/libraries/chartjs-adapter-date-fns": ["npm-asset/chartjs-adapter-date-fns"], "web/libraries/chartjs-plugin-datalabels": ["npm-asset/chartjs-plugin-datalabels"], "web/core": ["type:drupal-core"], "web/libraries/{$name}": ["type:drupal-library", "vendor:npm-asset"], "web/modules/contrib/{$name}": ["type:drupal-module"], "web/profiles/contrib/{$name}": ["type:drupal-profile"], "web/themes/contrib/{$name}": ["type:drupal-theme"], "drush/Commands/{$name}": ["type:drupal-drush"]}, "composer-exit-on-patch-failure": true, "patches": {"drupal/core": {"Cannot read property 'replace' of undefined - #2922677": "https://www.drupal.org/files/issues/2023-02-15/2922677-33.patch", "#disable_inline_form_errors_summary - #2880011": "https://www.drupal.org/files/issues/2023-08-22/2880011-107_0.patch", "Tranlsate Entity view/form mode - #2546212": "https://www.drupal.org/files/issues/2020-12-04/2546212-168.patch", "Tranlsate base field overrides - #2869124": "https://www.drupal.org/files/issues/2021-09-08/2869124-14.patch", "Language cache - #3221375": "./config/patches/language_labels.patch", "Querying with NULL values results in warning mb_strtolower() - #3302838": "https://www.drupal.org/files/issues/2022-08-23/3302838-13.patch", "Empty original entity": "./config/patches/empty_original.patch", "Uncaught ReferenceError: loadjs is not defined - #3336143": "https://git.drupalcode.org/project/drupal/-/merge_requests/3352.diff", "Library definitions have a license with no URL - #3359497": "https://www.drupal.org/files/issues/2023-10-07/fix-license-missing-url.patch", "File state": "https://www.drupal.org/files/issues/2022-08-20/2847425-65.patch", "PO files have no public URL - #2449895": "https://www.drupal.org/files/issues/2021-12-19/2449895_92.patch", "Combine fields filter case-sensitive search": "https://www.drupal.org/files/issues/2022-11-03/3318953-2.patch", "Context translation search - #2123543": "https://www.drupal.org/files/issues/2024-08-01/drupal-translate_string_context_filter-2123543-105.patch.patch"}, "drupal/baguettebox": {"BaguetteBox library Not found - #3248758": "https://www.drupal.org/files/issues/2022-11-04/baguettebox-library-not-found-3248758-34.patch"}, "drupal/leaflet": {"Allow altering maps per user or role": "./config/patches/lealet_alter.patch", "Fix map update on text change": "./config/patches/leaflet_update_map.patch"}, "drupal/commerce": {"Product variation title does not change when product title changes - #2929721": "https://www.drupal.org/files/issues/2020-09-27/2929721-29.patch"}, "drupal/commerce_invoice": {"Commerce invoice pay email": "./config/patches/commerce_invoice_pay_email.patch"}, "drupal/autocomplete_deluxe": {"Multiple entity reference field when you add new terms - #3053986": "https://www.drupal.org/files/issues/2021-05-25/3053986-6.patch"}, "drupal/simplenews": {"Simplenews blocks bulk user update": "./config/patches/simplenews_user_cache.patch"}, "drupal/views_aggregator": {"Calculating column sum - #3241057": "https://www.drupal.org/files/issues/2024-10-22/trouble-calculating-column-sum-on-views-group_by-3241057-7.patch"}, "drupal/quick_node_clone": {"Cloning simplenews issue copies sending status - #3385912": "https://www.drupal.org/files/issues/2023-09-07/clone_simplenews_issue.patch", "Nested paragraph support - #3183249": "https://www.drupal.org/files/issues/2025-01-16/quick_node_clone-nested-paragraphs-support-3183249-20.patch"}, "drupal/google_place_autocomplete": {"Jquery once - #3377648": "https://www.drupal.org/files/issues/2023-07-28/google_place_autocomplete_jquery_once_3377648.patch", "hook_field_widget_WIDGET_TYPE_form_alter - #3377663": "https://www.drupal.org/files/issues/2023-07-28/google_place_autocomplete_hook_deprecated_3377663.patch"}, "drupal/entity_print": {"Session is not set when generating PDFs  - #3380359": "https://git.drupalcode.org/project/entity_print/-/merge_requests/45.diff"}, "drupal/queue_mail": {"Support Symfony Mailer module - #3279936": "./config/patches/queue_mail_symfony_language.patch"}, "drupal/views_infinite_scroll": {"infinite scroll does not work for multiple views on a single page - #2877652": "https://www.drupal.org/files/issues/2022-08-17/2877652-33-fix_views_infinite_scroll_pager_when_multi_views_on_single_page.patch"}, "drupal/simple_recaptcha": {"Add multi-lingual / language support - #3203569": "https://www.drupal.org/files/issues/2021-03-15/simple_recaptcha_language_support-3203569-2.patch"}, "drupal/ckeditor5_embedded_content": {"CKEditor5 embedded content - #3424169": "./config/patches/ckeditor5_embedded_content.patch"}, "drupal/date_popup": {"Add 24h to end time to include last day.": "./config/patches/date_popup-add-time-to.patch"}, "drupal/image_field_360": {"8.3 deprication fixes - #3380758": "https://www.drupal.org/files/issues/2023-08-11/image_field_360-deprecated_dynamic_property_creation-3380758-3.patch"}, "klix/klix-sdk-php": {"Jsonserialize deprication fix": "./config/patches/jsonserializer_patch.patch"}, "drupal/googlelogin-googlelogin": {"Error 400: redirect_uri_mismatch with Destination Parameter - #3492997": "./config/patches/googlelogin.patch"}, "drupal/google_tag": {"Processing commands config ID missing - #3488123": "https://git.drupalcode.org/project/google_tag/-/merge_requests/96.diff", "Sign Up and Login events do not work - #3406422": "https://git.drupalcode.org/project/google_tag/-/merge_requests/62.diff", "gtag.js/gtm.js order in D10.4/D11 vs default consent #3512549": "https://www.drupal.org/files/issues/2025-03-12/gtag_js_gtm_js_order_d10.4_d11-3512549-2.patch", "Default consent set too late - #3465282": "https://www.drupal.org/files/issues/2024-11-13/default_consent_set_too_late-3465282-5.patch"}, "drupal/message": {"Messages do not show up if not translated": "./config/patches/message/language_fix.patch"}, "drupal/domain": {"Config UI creates *.yml files with duplicate UUIDs - #3222413": "https://www.drupal.org/files/issues/2025-01-29/3222413-41-duplicate-uuid.patch"}}}}