<?php

namespace <PERSON><PERSON>al\add_to_calendar;

use <PERSON><PERSON><PERSON>\auctions\AuctionInterface;
use <PERSON><PERSON><PERSON>\Core\Config\ConfigFactoryInterface;
use <PERSON><PERSON>al\Core\Datetime\DrupalDateTime;
use <PERSON><PERSON>al\Core\Extension\ExtensionPathResolver;
use <PERSON><PERSON><PERSON>\Core\Url;

/**
 * Service for generating add to calendar render arrays.
 */
class AddToCalendarService {

  /**
   * The extension path resolver.
   *
   * @var \Drupal\Core\Extension\ExtensionPathResolver
   */
  protected $extensionPathResolver;

  /**
   * The config factory.
   *
   * @var \Drupal\Core\Config\ConfigFactoryInterface
   */
  protected $configFactory;

  /**
   * Constructs a new AddToCalendarService object.
   *
   * @param \Drupal\Core\Extension\ExtensionPathResolver $extension_path_resolver
   *   The extension path resolver.
   * @param \Drupal\Core\Config\ConfigFactoryInterface $config_factory
   *   The config factory.
   */
  public function __construct(ExtensionPathResolver $extension_path_resolver, ConfigFactoryInterface $config_factory) {
    $this->extensionPathResolver = $extension_path_resolver;
    $this->configFactory = $config_factory;
  }

  /**
   * Builds add to calendar render array for an auction.
   *
   * @param \Drupal\auctions\AuctionInterface $auction
   *   The auction entity.
   *
   * @return array
   *   The render array for add to calendar links, or empty array if not applicable.
   */
  public function buildAddToCalendarRenderArray(AuctionInterface $auction): array {
    // Check if end time exists and auction is active.
    if (!$auction->hasField('end_time') || $auction->get('end_time')->isEmpty() || !$auction->isActive()) {
      return [];
    }

    // Gather event details.
    $title = t('Mezabirza.lv auction: @title', [
      '@title' => $auction->label(),
    ]);
    $end_time = new DrupalDateTime($auction->get('end_time')->value);
    $start_time = clone $end_time;
    $start_time->modify('-15 minutes');
    $end_time->modify('+15 minutes');
    $description = t('Link to auction: @url', [
      '@url' => $auction->toLink($auction->label())->toString(),
    ]);
    $utc_timezone = new \DateTimeZone($this->configFactory->get('system.date')->get('timezone.default'));
    $start_time->setTimezone($utc_timezone);
    $end_time->setTimezone($utc_timezone);
    $start_formatted = $start_time->format('Ymd\THis\Z');
    $end_formatted = $end_time->format('Ymd\THis\Z');

    // Generate direct calendar links.
    $google_link = $this->generateGoogleCalendarLink($title, $start_formatted, $end_formatted, $description);
    $outlook_link = $this->generateOutlookCalendarLink($title, $start_formatted, $end_formatted, $description);

    // Generate ICS content.
    $ics_content = $this->generateIcsContent($title, $start_formatted, $end_formatted, $description);
    $ics_link = 'data:text/calendar;charset=utf8;base64,' . base64_encode($ics_content);

    // Build the render array.
    return [
      '#theme' => 'add_to_calendar_links',
      '#google_link' => Url::fromUri($google_link)->toString(),
      '#google_icon' => '/' . $this->extensionPathResolver->getPath('module', 'add_to_calendar') . '/assets/google_icon.svg',
      '#outlook_link' => Url::fromUri($outlook_link)->toString(),
      '#outlook_icon' => '/' . $this->extensionPathResolver->getPath('module', 'add_to_calendar') . '/assets/outlook_icon.svg',
      '#ics_link' => $ics_link,
      '#ics_icon' => '/' . $this->extensionPathResolver->getPath('module', 'add_to_calendar') . '/assets/ical_icon.svg',
      '#attached' => ['library' => ['add_to_calendar/add_to_calendar']],
      '#cache' => ['contexts' => ['user']],
    ];
  }

  /**
   * Generate ICS calendar data.
   *
   * @param string $title
   *   The title of the event.
   * @param string $start_formatted
   *   The formatted start time.
   * @param string $end_formatted
   *   The formatted end time.
   * @param string $description
   *   The description of the event.
   *
   * @return string
   *   The ICS calendar data.
   */
  protected function generateIcsContent($title, $start_formatted, $end_formatted, $description) {
    $uid = uniqid();
    $now = gmdate('Ymd\THis\Z');

    $ics = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//Mezabirza//NONSGML Add to Calendar//EN',
      'METHOD:PUBLISH',
      'BEGIN:VEVENT',
      'UID:' . $uid,
      'DTSTAMP:' . $now,
      'DTSTART:' . $start_formatted,
      'DTEND:' . $end_formatted,
      'SUMMARY:' . $title,
      'DESCRIPTION:' . str_replace("\n", "\\n", $description),
      'END:VEVENT',
      'END:VCALENDAR',
    ];

    return implode("\r\n", $ics);
  }

  /**
   * Generate Google Calendar link.
   *
   * @param string $title
   *   The title of the event.
   * @param string $start_time
   *   The start time of the event.
   * @param string $end_time
   *   The end time of the event.
   * @param string $description
   *   The description of the event.
   *
   * @return string
   *   The Google Calendar link.
   */
  protected function generateGoogleCalendarLink($title, $start_time, $end_time, $description) {
    $dates = $start_time . '/' . $end_time;

    // Encode parameters to handle special characters.
    $title = urlencode($title);
    $description = urlencode($description);

    // Construct the Google Calendar URL.
    return "https://www.google.com/calendar/render?action=TEMPLATE&text={$title}&dates={$dates}&details={$description}";
  }

  /**
   * Generate Outlook Calendar link.
   *
   * @param string $title
   *   The title of the event.
   * @param string $start_time
   *   The start time of the event.
   * @param string $end_time
   *   The end time of the event.
   * @param string $description
   *   The description of the event.
   *
   * @return string
   *   The Outlook Calendar link.
   */
  protected function generateOutlookCalendarLink($title, $start_time, $end_time, $description) {
    // Encode parameters.
    $title = urlencode($title);
    $description = urlencode($description);

    // Construct the Outlook URL.
    return "https://outlook.live.com/owa/?path=/calendar/action/compose&subject={$title}&startdt={$start_time}&enddt={$end_time}&body={$description}";
  }

}
