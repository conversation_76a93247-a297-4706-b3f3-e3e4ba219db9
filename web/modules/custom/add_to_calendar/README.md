# Add to Calendar Module

This module provides add-to-calendar functionality for auction entities. It generates calendar links for Google Calendar, Outlook, and ICS files.

## Service Usage

The module provides a service `add_to_calendar.service` that can be used to generate add-to-calendar render arrays for any AuctionInterface entity.

### Service Method

```php
/**
 * Builds add to calendar render array for an auction.
 *
 * @param \Drupal\auctions\AuctionInterface $auction
 *   The auction entity.
 *
 * @return array
 *   The render array for add to calendar links, or empty array if not applicable.
 */
public function buildAddToCalendarRenderArray(AuctionInterface $auction): array
```

### Usage in Entity View

The service is automatically used in the entity view hook:

```php
function add_to_calendar_entity_view(array &$build, EntityInterface $entity, EntityViewDisplayInterface $display, $view_mode) {
  if ($display->getComponent('add_to_calendar') && $entity instanceof \Drupal\auctions\AuctionInterface) {
    $build['add_to_calendar'] = \Drupal::service('add_to_calendar.service')->buildAddToCalendarRenderArray($entity);
  }
}
```

### Usage in Email Context

To use the add-to-calendar functionality in emails, you can call the service and render the result:

```php
// Example: In an email sending function
function sendAuctionEmail(AuctionInterface $auction, $recipient) {
  $add_to_calendar_service = \Drupal::service('add_to_calendar.service');
  $add_to_calendar_build = $add_to_calendar_service->buildAddToCalendarRenderArray($auction);
  
  if (!empty($add_to_calendar_build)) {
    $renderer = \Drupal::service('renderer');
    $add_to_calendar_html = $renderer->render($add_to_calendar_build);
    
    // Include $add_to_calendar_html in your email body
    $params['body'][] = $add_to_calendar_html;
  }
  
  // Send email...
}
```

### Usage in Custom Code

```php
// Load an auction
$auction = \Drupal\auctions\Entity\Auction::load($auction_id);

// Get the add-to-calendar render array
$add_to_calendar_service = \Drupal::service('add_to_calendar.service');
$build = $add_to_calendar_service->buildAddToCalendarRenderArray($auction);

// Use the render array in your context
if (!empty($build)) {
  // Render it
  $renderer = \Drupal::service('renderer');
  $html = $renderer->render($build);
  
  // Or add it to another render array
  $page_build['add_to_calendar'] = $build;
}
```

## Requirements

- The auction entity must have an `end_time` field that is not empty
- The auction must be in active status (`$auction->isActive()` returns TRUE)

## Generated Links

The service generates three types of calendar links:

1. **Google Calendar** - Direct link to add event to Google Calendar
2. **Outlook** - Direct link to add event to Outlook Calendar  
3. **ICS File** - Data URI with ICS calendar file for download

## Event Details

The calendar event includes:

- **Title**: "Mezabirza.lv auction: [auction label]"
- **Start Time**: 15 minutes before auction end time
- **End Time**: 15 minutes after auction end time  
- **Description**: Link to the auction page
- **Timezone**: System default timezone converted to UTC

## Template

The render array uses the `add_to_calendar_links` theme template located at:
`templates/add-to-calendar-links.html.twig`
