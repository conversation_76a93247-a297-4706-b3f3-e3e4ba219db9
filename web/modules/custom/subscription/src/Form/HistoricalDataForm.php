<?php

namespace Drupal\subscription\Form;

use Drupal\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Form\FormBase;
use <PERSON><PERSON><PERSON>\Core\Form\FormBuilderInterface;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\Core\Session\AccountProxyInterface;
use Drupal\auctions\AuctionsHelper;
use Drupal\userpoints\TransactionHandler;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides a Subscription form.
 */
class HistoricalDataForm extends FormBase {

  /**
   * The transaction handler.
   *
   * @var \Drupal\userpoints\TransactionHandler
   */
  protected $transactionHandler;

  /**
   * The bid.
   *
   * @var \Drupal\auctions\BidInterface
   */
  protected $bid;


  /**
   * The form builder.
   *
   * @var \Drupal\Core\Form\FormBuilderInterface
   */
  protected $formBuilder;

  /**
   * The auction.
   *
   * @var \Drupal\auctions\AuctionInterface
   */
  protected $auction;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountProxyInterface
   */
  protected $currentUser;

  /**
   * The auctions helper.
   *
   * @var \Drupal\auctions\AuctionsHelper
   */
  protected $auctionsHelper;

  /**
   * The bid storage.
   *
   * @var \Drupal\Core\Entity\EntityStorageInterface
   */
  protected $bidStorage;

  /**
   * Constructs a new AuctionBidForm.
   *
   * @param \Drupal\userpoints\TransactionHandler $transaction_handler
   *   The transaction handler.
   * @param \Drupal\Core\Form\FormBuilderInterface $form_builder
   *   The form builder.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The form builder.
   * @param \Drupal\Core\Session\AccountProxyInterface $account_proxy
   *   The form builder.
   * @param \Drupal\auctions\AuctionsHelper $auctions_helper
   *   The auctions helper.
   *
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   */
  public function __construct(
    TransactionHandler $transaction_handler,
    FormBuilderInterface $form_builder,
    EntityTypeManagerInterface $entity_type_manager,
    AccountProxyInterface $account_proxy,
    AuctionsHelper $auctions_helper,
  ) {
    $this->transactionHandler = $transaction_handler;
    $this->formBuilder = $form_builder;
    $this->entityTypeManager = $entity_type_manager;
    $this->currentUser = $account_proxy;
    $this->auctionsHelper = $auctions_helper;
    $this->bidStorage = $this->entityTypeManager->getStorage('bid');
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('userpoints.transaction_handler'),
      $container->get('form_builder'),
      $container->get('entity_type.manager'),
      $container->get('current_user'),
      $container->get('auctions.helper')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'subscription_historical_data';
  }

  /**
   * Gets subscription options.
   *
   * @return array
   *   Options.
   */
  public function getOptions() {
    return [
      [
        'fee' => -10,
        'key' => '1-day',
        'option' => $this->t('@number Day (@points Points)', [
          '@number' => 1,
          '@points' => 10,
        ]),
      ],
      [
        'fee' => -25,
        'key' => '1-month',
        'option' => $this->t('@number Month (@points Points)', [
          '@number' => 1,
          '@points' => 25,
        ]),
      ],
      [
        'fee' => -100,
        'key' => '6-month',
        'option' => $this->t('@number Months (@points Points)', [
          '@number' => 6,
          '@points' => 100,
        ]),
      ],
      [
        'fee' => -150,
        'key' => '1-year',
        'option' => $this->t('@number Year (@points Points)', [
          '@number' => 1,
          '@points' => 150,
        ]),
      ],
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $options = [];
    foreach ($this->getOptions() as $option) {
      $options[$option['key']] = $option['option'];
    }
    $form['subscription'] = [
      '#type' => 'radios',
      '#title' => $this->t('Subscription plan'),
      '#options' => $options,
      '#required' => TRUE,
    ];
    $form['userpoints'] = [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#value' => $this->t('Your balance is: @points', [
        '@points' => $this->transactionHandler->getCurrentUserBalance(),
      ]),
    ];
    $form['actions'] = [
      '#type' => 'actions',
    ];
    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Subscribe'),
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    if (subscription_is_subscribed()) {
      $form_state->setError($form, $this->t('You already have active subscription to historical data.'));
    }
    $option = $this->getOptions()[array_search($form_state->getValue('subscription'), array_values(array_column($this->getOptions(), 'key')))];
    if ($option === FALSE) {
      $form_state->setErrorByName('subscription', $this->t('Invalid option selected.'));
    }
    if (!$this->transactionHandler->userIsSolventToPay($this->currentUser, (-1 * $option['fee']))) {
      $form_state->setError($form, $this->t('You have insufficient funds to make this subscription.'));
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $option = $this->getOptions()[array_search($form_state->getValue('subscription'), array_values(array_column($this->getOptions(), 'key')))];
    [$duration, $unit] = explode('-', $option['key']);
    $subscription = $this->entityTypeManager->getStorage('subscription')->create([
      'name' => 'historical_data',
      'duration' => $duration,
      'duration_unit' => $unit,
      'expires' => strtotime('+' . str_replace('-', ' ', $option['key'])),
      'status' => TRUE,
      'uid' => $this->currentUser->id(),
    ]);
    $subscription->save();
    $this->transactionHandler->deduct($subscription->name->value, $subscription);
    $this->messenger()->addStatus($this->t('You have subscribed to historical data.'));
    $form_state->setRedirect('view.user_subscriptions.user', ['user' => $this->currentUser->id()]);
  }

}
