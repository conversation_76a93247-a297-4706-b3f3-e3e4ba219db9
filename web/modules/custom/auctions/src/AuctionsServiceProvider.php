<?php

namespace Drupal\auctions;

use <PERSON><PERSON>al\Core\DependencyInjection\ContainerBuilder;
use Drupal\Core\DependencyInjection\ServiceProviderBase;
use Drupal\Core\DependencyInjection\ServiceProviderInterface;
use Symfony\Component\DependencyInjection\Reference;

/**
 * Class AuctionsServiceProvider alters default mailer.
 *
 * @package Drupal\auctions
 */
class AuctionsServiceProvider extends ServiceProviderBase implements ServiceProviderInterface {

  /**
   * {@inheritdoc}
   */
  public function alter(ContainerBuilder $container) {
    $container->getDefinition('simplenews.mailer')
      ->setClass('Drupal\auctions\Mail\Mailer')
      ->addArgument(new Reference('language.default'));
    $container->getDefinition('commerce_invoice.invoice_confirmation_subscriber')
      ->setClass('\Drupal\auctions\EventSubscriber\AuctionInvoiceConfirmationSubscriber');
  }

}
