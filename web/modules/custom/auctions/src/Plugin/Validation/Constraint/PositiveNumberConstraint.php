<?php

namespace Drupal\auctions\Plugin\Validation\Constraint;

use Symfony\Component\Validator\Constraint;

/**
 * Provides a PositiveNumber constraint.
 *
 * @Constraint(
 *   id = "PositiveNumber",
 *   label = @Translation("PositiveNumber", context = "Validation"),
 * )
 */
class PositiveNumberConstraint extends Constraint {

  /**
   * Message.
   *
   * @var string
   */
  public $errorMessage = 'Price must be positive.';

}
