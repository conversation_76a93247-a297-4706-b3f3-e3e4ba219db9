<?php

namespace Drupal\auctions\Plugin\Validation\Constraint;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

/**
 * Class StepCompatiblePriceConstraintValidator.
 *
 * Validates the StepCompatiblePrice constraint.
 *
 * @package Drupal\auctions\Plugin\Validation\Constraint
 */
class StepCompatiblePriceConstraintValidator extends ConstraintValidator {

  /**
   * {@inheritdoc}
   */
  public function validate($items, Constraint $constraint) {
    foreach ($items as $delta => $item) {
      // Get the auction entity.
      $entity = $items->getEntity();

      // Skip validation if we don't have an auction entity.
      if (!$entity || $entity->getEntityTypeId() !== 'auction') {
        continue;
      }

      // Determine the step size based on auction type.
      // Default step size.
      $step_size = 100;

      if ($entity->isType('custom')) {
        // For custom auctions, use field_step value or fallback to 100.
        $step_size = $entity->getStep();
      }

      // Check if the start price is divisible by the step size.
      if ($item->value && fmod($item->value, $step_size) !== 0.0) {
        $this->context->buildViolation($constraint->errorMessage)
          ->setParameter('@size', number_format($step_size, ($step_size < 1 ? 2 : 0), '.', ''))
          ->atPath($delta)
          ->addViolation();
      }
    }
  }

}
