<?php

namespace Drupal\auctions\Plugin\Validation\Constraint;

use Symfony\Component\Validator\Constraint;

/**
 * Provides a StepCompatiblePrice constraint.
 *
 * @Constraint(
 *   id = "StepCompatiblePrice",
 *   label = @Translation("Step Compatible Price", context = "Validation"),
 * )
 */
class StepCompatiblePriceConstraint extends Constraint {

  /**
   * The error message.
   *
   * @var string
   */
  public $errorMessage = 'Start price must be compatible with bid step size which is @size';

}
