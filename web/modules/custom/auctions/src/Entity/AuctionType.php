<?php

namespace Drupal\auctions\Entity;

use Drupal\Core\Config\Entity\ConfigEntityBundleBase;

/**
 * Defines the Auction type configuration entity.
 *
 * @ConfigEntityType(
 *   id = "auction_type",
 *   label = @Translation("Auction type"),
 *   handlers = {
 *     "access" = "Drupal\auctions\AuctionTypeAccessControlHandler",
 *     "form" = {
 *       "add" = "Drupal\auctions\Form\AuctionTypeForm",
 *       "edit" = "Drupal\auctions\Form\AuctionTypeForm",
 *       "delete" = "Drupal\Core\Entity\EntityDeleteForm",
 *     },
 *     "list_builder" = "Drupal\auctions\AuctionTypeListBuilder",
 *     "route_provider" = {
 *       "html" = "Drupal\Core\Entity\Routing\AdminHtmlRouteProvider",
 *     }
 *   },
 *   admin_permission = "administer auction types",
 *   bundle_of = "auction",
 *   config_prefix = "auction_type",
 *   entity_keys = {
 *     "id" = "id",
 *     "label" = "label",
 *     "uuid" = "uuid"
 *   },
 *   links = {
 *     "add-form" = "/admin/structure/auction_types/add",
 *     "edit-form" = "/admin/structure/auction_types/manage/{auction_type}",
 *     "delete-form" = "/admin/structure/auction_types/manage/{auction_type}/delete",
 *     "collection" = "/admin/structure/auction_types"
 *   },
 *   config_export = {
 *     "id",
 *     "label",
 *     "uuid",
 *   }
 * )
 */
class AuctionType extends ConfigEntityBundleBase {

  /**
   * The machine name of this auction type.
   *
   * @var string
   */
  protected $id;

  /**
   * The human-readable name of the auction type.
   *
   * @var string
   */
  protected $label;

}
