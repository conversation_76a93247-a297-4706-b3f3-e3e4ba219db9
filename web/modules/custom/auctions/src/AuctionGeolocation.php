<?php

declare(strict_types=1);

namespace Dr<PERSON>al\auctions;

use <PERSON><PERSON><PERSON>\geofield\GeoPHP\GeoPHPWrapper;
use GuzzleHttp\Client;

/**
 * Provides a service for geolocation operations.
 */
class AuctionGeolocation {

  /**
   * The GeoPHP wrapper service.
   *
   * @var \Drupal\geofield\GeoPHP\GeoPHPWrapper
   */
  protected GeoPHPWrapper $geofieldGeophp;

  /**
   * The HTTP client service.
   *
   * @var \GuzzleHttp\Client
   */
  protected Client $httpClient;

  /**
   * The controller constructor.
   *
   * @param \Drupal\geofield\GeoPHP\GeoPHPWrapper $geo_field_geo_php
   *   The GeoPHP wrapper service.
   * @param \GuzzleHttp\Client $http_client
   *   The HTTP client service.
   */
  public function __construct(GeoPHPWrapper $geo_field_geo_php, Client $http_client) {
    $this->geofieldGeophp = $geo_field_geo_php;
    $this->httpClient = $http_client;
  }

  /**
   * Merges cadastre coordinates with the GeoJSON data.
   *
   * @param string $cadastre
   *   The cadastre number.
   * @param string $geo_data
   *   The GeoJSON data.
   */
  public function mergeCadastreCoordinates(string $cadastre, string $geo_data) {
    $url = 'https://lvmgeoserver.lvm.lv/geoserver/publicwfs/wfs?service=wfs&version=2.0.0&request=GetFeature&typename=publicwfs:kkparcel&outputFormat=json&srsname=EPSG:4326&cql_filter=code=%27' . $cadastre . '%27';
    $response = $this->httpClient->request('GET', $url);
    $data = json_decode($response->getBody()->getContents(), TRUE);
    if (empty($data['features'][0]['geometry']['coordinates'])) {
      return FALSE;
    }

    // Load the geometry using the GeoPHP service.
    $geometry = $this->geofieldGeophp->load(json_encode($data['features'][0]['geometry']), 'geojson');

    // Ensure the geometry is valid and supports centroid calculation.
    if (!$geometry || !method_exists($geometry, 'getCentroid')) {
      return FALSE;
    }

    // Calculate the centroid.
    $centroid = $geometry->getCentroid();

    // Build the GeoJSON Feature Point.
    $centroid = [
      'type' => 'Feature',
      'geometry' => [
        'type' => 'Point',
        'coordinates' => [$centroid->x(), $centroid->y()],
      ],
      'properties' => [],
    ];

    // Add the centroid to the GeoJSON data.
    $geo_data = json_decode($geo_data, TRUE) ?: [];
    return $this->addFeatureToGeoData($centroid, $geo_data);
  }

  /**
   * Adds a new feature to the GeoJSON data.
   *
   * @param array $new_feature
   *   The new feature to add.
   * @param array $geo_data
   *   The existing GeoJSON data.
   *
   * @return array
   *   The updated GeoJSON data.
   */
  private function addFeatureToGeoData(array $new_feature, array $geo_data = []): array {
    // If empty, return the new feature as the base structure.
    if (empty($geo_data)) {
      return [
        'type' => 'FeatureCollection',
        'features' => [$new_feature],
      ];
    }

    // Convert to FeatureCollection if it's a single Feature.
    if ($geo_data['type'] === 'Feature') {
      $geo_data = [
        'type' => 'FeatureCollection',
        'features' => [$geo_data],
      ];
    }
    elseif ($geo_data['type'] !== 'FeatureCollection') {
      $geo_data = [
        'type' => 'FeatureCollection',
        'features' => [$geo_data],
      ];
    }

    // Check for duplicates in the current features.
    foreach ($geo_data['features'] as $existing_feature) {
      if ($this->featuresAreEqual($existing_feature, $new_feature)) {
        // If the feature already exists, return without adding.
        return $geo_data;
      }
    }

    // Add the new feature to the GeoJSON data.
    $geo_data['features'][] = $new_feature;

    return $geo_data;
  }

  /**
   * Checks if two GeoJSON features are equal.
   *
   * @param array $feature1
   *   The first feature.
   * @param array $feature2
   *   The second feature.
   *
   * @return bool
   *   TRUE if the features are equal, FALSE otherwise.
   */
  private function featuresAreEqual(array $feature1, array $feature2): bool {
    // Convert to JSON to compare.
    return json_encode($feature1) === json_encode($feature2);
  }

  /**
   * Merges CA coordinates with the GeoJSON data.
   *
   * @param string $ca
   *   The CA number.
   * @param string $geo_data
   *   The GeoJSON data.
   */
  public function mergeCaCoordinates(string $ca, string $geo_data) {
    $ca_data = $this->getCaData($ca);
    if (!$ca_data) {
      return FALSE;
    }

    // Add each feature to the GeoJSON data one by one.
    $geo_data = json_decode($geo_data, TRUE) ?: [];
    foreach ($ca_data as $feature) {
      $geo_data = $this->addFeatureToGeoData($feature, $geo_data);
    }

    return $geo_data;
  }

  /**
   * Get CA data from the VMD API.
   *
   * @param string $ca
   *   The CA number.
   *
   * @return array|bool
   *   The GeoJSON data or FALSE if the data is not found.
   */
  private function getCaData(string $ca) {
    // Post to https://gis.vmd.gov.lv/Public/AffirmationSearch with ca as aff.
    $url = 'https://gis.vmd.gov.lv/Public/AffirmationSearch';
    $response = $this->httpClient->request('POST', $url, [
      'form_params' => [
        'aff' => $ca,
      ],
    ]);
    if ($response->getStatusCode() !== 200) {
      return FALSE;
    }
    $data = json_decode($response->getBody()->getContents(), TRUE)['html'];

    // Extract all unique data-crypt-id values from html.
    preg_match_all('/data-crypt-id="([^"]+)"/', $data, $matches);
    $crypt_ids = array_unique($matches[1]);

    $geojson_features = [];
    foreach ($crypt_ids as $crypt_id) {
      $url = 'https://gis.vmd.gov.lv/Public/GetFellCoordinates?fellId=' . $crypt_id;
      $table_response = $this->httpClient->request('GET', $url);
      if ($table_response->getStatusCode() !== 200) {
        continue;
      }
      $geojson_feature = $this->extractGeoJsonCoordinates($table_response->getBody()->getContents());
      if ($geojson_feature) {
        $geojson_features[] = $geojson_feature;
      }
    }

    return $geojson_features;
  }

  /**
   * Extracts GeoJSON coordinates from the VMD HTML table.
   *
   * @param string $html
   *   The HTML table.
   *
   * @return array|bool
   *   The GeoJSON coordinates or FALSE if the coordinates are not found.
   */
  public function extractGeoJsonCoordinates($html) {
    // Load HTML with DOMDocument.
    $dom = new \DOMDocument();
    // Suppress HTML parsing warnings.
    libxml_use_internal_errors(TRUE);
    $dom->loadHTML($html);
    libxml_clear_errors();

    // Use XPath to find the cell containing "WGS84 DD koordinātas".
    $xpath = new \DOMXPath($dom);
    $coordinatesCell = $xpath->query('//table[@id="tbl-decisions"]/tbody/tr/td[3]');

    if ($coordinatesCell->length > 0) {
      // Extract the node containing the coordinates.
      $coordinatesNode = $coordinatesCell->item(0);

      // Get all text nodes and <br> tags to account for all coordinates.
      $coordinates = [];
      foreach ($coordinatesNode->childNodes as $child) {
        if ($child->nodeType === XML_TEXT_NODE) {
          // Split text node by line breaks and trim whitespace.
          $textParts = array_filter(array_map('trim', explode("\n", $child->nodeValue)));
          foreach ($textParts as $part) {
            $coordinates[] = $part;
          }
        }
      }

      // Process all coordinates.
      $geojsonCoordinates = [];
      foreach ($coordinates as $pair) {
        if (strpos($pair, ',') !== FALSE) {
          [$lon, $lat] = array_map('floatval', explode(',', $pair));
          $geojsonCoordinates[] = [$lon, $lat];
        }
      }

      // Close the polygon by repeating the first coordinate at the end.
      if (!empty($geojsonCoordinates)) {
        $geojsonCoordinates[] = $geojsonCoordinates[0];
      }

      // Return the GeoJSON Polygon array format.
      return [
        'type' => 'Feature',
        'geometry' => [
          'type' => 'Polygon',
          'coordinates' => [$geojsonCoordinates],
        ],
      ];
    }

    // Return FALSE if the coordinates are not found.
    return FALSE;
  }

}
