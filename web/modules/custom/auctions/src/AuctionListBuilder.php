<?php

namespace Drupal\auctions;

use <PERSON><PERSON>al\Core\Datetime\DateFormatterInterface;
use Drupal\Core\Entity\EntityInterface;
use <PERSON><PERSON>al\Core\Entity\EntityListBuilder;
use <PERSON><PERSON>al\Core\Entity\EntityStorageInterface;
use <PERSON><PERSON>al\Core\Entity\EntityTypeInterface;
use Drupal\Core\Routing\RedirectDestinationInterface;
use Drupal\Core\Session\AccountProxyInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides a list controller for the auction entity type.
 */
class AuctionListBuilder extends EntityListBuilder {

  /**
   * The date formatter service.
   *
   * @var \Drupal\Core\Datetime\DateFormatterInterface
   */
  protected DateFormatterInterface $dateFormatter;

  /**
   * The redirect destination service.
   *
   * @var \Drupal\Core\Routing\RedirectDestinationInterface
   */
  protected $redirectDestination;

  /**
   * Current user.
   *
   * @var \Drupal\Core\Session\AccountProxyInterface
   */
  protected AccountProxyInterface $currentUser;

  /**
   * Constructs a new AuctionListBuilder object.
   *
   * @param \Drupal\Core\Entity\EntityTypeInterface $entity_type
   *   The entity type definition.
   * @param \Drupal\Core\Entity\EntityStorageInterface $storage
   *   The entity storage class.
   * @param \Drupal\Core\Datetime\DateFormatterInterface $date_formatter
   *   The date formatter service.
   * @param \Drupal\Core\Routing\RedirectDestinationInterface $redirect_destination
   *   The redirect destination service.
   * @param \Drupal\Core\Session\AccountProxyInterface $current_user
   *   Current user.
   */
  public function __construct(EntityTypeInterface $entity_type, EntityStorageInterface $storage, DateFormatterInterface $date_formatter, RedirectDestinationInterface $redirect_destination, AccountProxyInterface $current_user) {
    parent::__construct($entity_type, $storage);
    $this->dateFormatter = $date_formatter;
    $this->redirectDestination = $redirect_destination;
    $this->currentUser = $current_user;
  }

  /**
   * {@inheritdoc}
   */
  public static function createInstance(ContainerInterface $container, EntityTypeInterface $entity_type) {
    return new static(
      $entity_type,
      $container->get('entity_type.manager')->getStorage($entity_type->id()),
      $container->get('date.formatter'),
      $container->get('redirect.destination'),
      $container->get('current_user')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function render() {
    $build['table'] = parent::render();

    $total = $this->getStorage()
      ->getQuery()
      ->accessCheck(FALSE)
      ->count()
      ->execute();

    $build['summary']['#markup'] = $this->t('Total auctions: @total', ['@total' => $total]);
    return $build;
  }

  /**
   * {@inheritdoc}
   */
  public function buildHeader() {
    $header['id'] = $this->t('ID');
    $header['title'] = $this->t('Title');
    $header['status'] = $this->t('Status');
    $header['uid'] = $this->t('Author');
    $header['created'] = $this->t('Created');
    $header['changed'] = $this->t('Updated');
    return $header + parent::buildHeader();
  }

  /**
   * {@inheritdoc}
   */
  public function buildRow(EntityInterface $entity) {
    /** @var \Drupal\auctions\AuctionInterface $entity */
    $row['id'] = $entity->id();
    $row['title'] = $entity->toLink();
    $row['status'] = $entity->isEnabled() ? $this->t('Enabled') : $this->t('Disabled');
    $row['uid']['data'] = [
      '#theme' => 'username',
      '#account' => $entity->getOwner(),
    ];
    $row['created'] = $this->dateFormatter->format($entity->getCreatedTime());
    $row['changed'] = $this->dateFormatter->format($entity->getChangedTime());
    return $row + parent::buildRow($entity);
  }

  /**
   * {@inheritdoc}
   */
  protected function getDefaultOperations(EntityInterface $entity) {
    /** @var \Drupal\auctions\AuctionInterface $entity */
    $operations = parent::getDefaultOperations($entity);
    if ($entity->access('update') && $entity->hasLinkTemplate('submit-form') && $entity->canBeSubmitted()) {
      $operations['submit'] = [
        'title' => $this->t('Submit for approval'),
        'weight' => 10,
        'url' => $this->ensureDestination($entity->toUrl('submit-form')),
      ];
    }
    if ($entity->access('update') && $entity->hasLinkTemplate('reject-form') && $entity->isPending() && \Drupal::currentUser()->hasPermission('change auction state')) {
      $operations['reject'] = [
        'title' => $this->t('Reject'),
        'weight' => 10,
        'url' => $this->ensureDestination($entity->toUrl('reject-form')),
      ];
    }
    if ($entity->access('update') && $entity->hasLinkTemplate('activate-form') && $entity->isPending() && \Drupal::currentUser()->hasPermission('change auction state')) {
      $operations['activate'] = [
        'title' => $this->t('Activate'),
        'weight' => 10,
        'url' => $this->ensureDestination($entity->toUrl('activate-form')),
      ];
    }
    if (
      ($entity->access('update') || $this->currentUser->hasPermission('change auction state')) &&
      (!$entity->hasBids() && ($entity->isFinished() || $entity->isActive()))
    ) {
      $operations['deactivate'] = [
        'title' => $this->t('Deactivate'),
        'weight' => 10,
        'url' => $this->ensureDestination($entity->toUrl('deactivate-form')),
      ];
    }
    if (
      ($entity->access('update') || $this->currentUser->hasPermission('change auction state')) &&
      (!$entity->hasBids() && $entity->isActive())
    ) {
      $operations['cancel'] = [
        'title' => $this->t('Cancel'),
        'weight' => 10,
        'url' => $this->ensureDestination($entity->toUrl('cancel-form')),
      ];
    }
    if ($entity->access('view')) {
      $operations = [
        'view' => [
          'title' => $this->t('View'),
          'weight' => 10,
          'url' => $this->ensureDestination($entity->toUrl()),
        ],
      ] + $operations;
    }

    $destination = $this->redirectDestination->getAsArray();
    foreach ($operations as &$operation) {
      $operation['query'] = $destination;
    }
    return $operations;
  }

}
