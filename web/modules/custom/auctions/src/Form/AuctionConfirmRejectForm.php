<?php

namespace Drupal\auctions\Form;

use <PERSON><PERSON>al\Component\Datetime\TimeInterface;
use <PERSON><PERSON>al\Core\Entity\ContentEntityForm;
use Dr<PERSON>al\Core\Entity\EntityRepositoryInterface;
use <PERSON><PERSON>al\Core\Entity\EntityTypeBundleInfoInterface;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Url;
use Drupal\userpoints\TransactionHandler;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Class AuctionConfirmRejectForm to reject auction.
 *
 * @package Drupal\auctions\Form
 */
class AuctionConfirmRejectForm extends ContentEntityForm {

  /**
   * Transaction handler.
   *
   * @var \Drupal\userpoints\TransactionHandler
   */
  public $transactionHandler;

  /**
   * Constructs a AuctionConfirmSubmitForm object.
   *
   * @param \Drupal\Core\Entity\EntityRepositoryInterface $entity_repository
   *   The entity repository service.
   * @param \Drupal\userpoints\TransactionHandler $transaction_handler
   *   The transaction handler.
   * @param \Drupal\Core\Entity\EntityTypeBundleInfoInterface $entity_type_bundle_info
   *   The entity type bundle service.
   * @param \Drupal\Component\Datetime\TimeInterface $time
   *   The time service.
   */
  public function __construct(
    EntityRepositoryInterface $entity_repository,
    TransactionHandler $transaction_handler,
    ?EntityTypeBundleInfoInterface $entity_type_bundle_info = NULL,
    ?TimeInterface $time = NULL,
  ) {
    parent::__construct($entity_repository, $entity_type_bundle_info, $time);
    $this->transactionHandler = $transaction_handler;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('entity.repository'),
      $container->get('userpoints.transaction_handler'),
      $container->get('entity_type.bundle.info'),
      $container->get('datetime.time')
    );
  }

  /**
   * Provides a generic edit title callback.
   *
   * @return string|null
   *   The title for the entity edit page, if an entity was found.
   */
  public function submitTitle() {
    if ($entity = $this->getRouteMatch()->getParameter('auction')) {
      return $this->t('Reject auction %label.', ['%label' => $entity->label()]);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'auctions_confirm_reject';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $form['message'] = [
      '#type' => 'html_tag',
      '#tag' => 'p',
      '#value' => $this->t('This action will reject auction @label.', ['@label' => $this->entity->label()]),
    ];
    $form['actions'] = [
      '#type' => 'actions',
      'submit' => [
        '#type' => 'submit',
        '#value' => $this->t('Reject'),
      ],
    ];
    $form['actions']['cancel'] = [
      '#title' => $this->t('Cancel'),
      '#type' => 'link',
      '#url' => $this->getCancelUrl(),
      '#attributes' => ['class' => ['button', 'button--danger']],
    ];

    return $form;
  }

  /**
   * Gets cancel action url.
   *
   * @return \Drupal\Core\Url
   *   Cancellation url.
   */
  public function getCancelUrl() {
    if ($destination = \Drupal::destination()->get()) {
      return Url::fromUserInput($destination);
    }
    return new Url('entity.auction.canonical', ['auction' => $this->entity]);
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    if (!$this->currentUser()->hasPermission('change auction state')) {
      $form_state->setError($form, t('You do not have permissions to reject this auction.'));
    }
    if (!$this->entity->isPending()) {
      $form_state->setError($form, t('You can only reject pending auctions.'));
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $this->entity->setDeclined()->save();
    $this->messenger()->addStatus($this->t('Auction rejected.'));
    $form_state->setRedirectUrl($this->getCancelUrl());
  }

}
