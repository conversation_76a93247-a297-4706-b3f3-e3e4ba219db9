<?php

namespace Drupal\auctions\Form;

use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON><PERSON>\Core\Form\ConfirmFormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\StringTranslation\TranslatableMarkup;
use <PERSON><PERSON><PERSON>\Core\TempStore\PrivateTempStoreFactory;
use Drupal\Core\Url;
use Drupal\userpoints\TransactionHandler;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides form to submit multiple auctions.
 *
 * @package Drupal\auctions\Form
 */
class SubmitMultipleAuctionsForm extends ConfirmFormBase {

  /**
   * The temp store factory.
   *
   * @var \Drupal\Core\TempStore\PrivateTempStoreFactory
   */
  protected $tempStoreFactory;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The transaction handler.
   *
   * @var \Drupal\userpoints\TransactionHandler
   */
  protected $transactionHandler;

  /**
   * Constructs a new UserMultipleCancelConfirm.
   *
   * @param \Drupal\Core\TempStore\PrivateTempStoreFactory $temp_store_factory
   *   The temp store factory.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\userpoints\TransactionHandler $transaction_handler
   *   The transaction handler.
   */
  public function __construct(PrivateTempStoreFactory $temp_store_factory, EntityTypeManagerInterface $entity_type_manager, TransactionHandler $transaction_handler) {
    $this->tempStoreFactory = $temp_store_factory;
    $this->entityTypeManager = $entity_type_manager;
    $this->transactionHandler = $transaction_handler;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('tempstore.private'),
      $container->get('entity_type.manager'),
      $container->get('userpoints.transaction_handler')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'auctions_submit_multiple_auctions';
  }

  /**
   * {@inheritdoc}
   */
  public function getQuestion() {
    return $this->t('Are you sure you want to submit these auctions?');
  }

  /**
   * {@inheritdoc}
   */
  public function getCancelUrl() {
    return new Url('view.user_auctions.page', ['user' => $this->currentUser()->id()]);
  }

  /**
   * {@inheritdoc}
   */
  public function getConfirmText() {
    return $this->t('Submit auctions');
  }

  /**
   * {@inheritdoc}
   */
  public function getDescription() {
    return $this->t('This action will submit the auction for approval. You will no longer be able to edit the auction (unless rejected).');
  }

  /**
   * Redirects away from form with error message.
   *
   * @param \Drupal\Core\StringTranslation\TranslatableMarkup $message
   *   Message.
   *
   * @return \Symfony\Component\HttpFoundation\RedirectResponse
   *   A redirect response object that may be returned by the controller.
   */
  protected function redirectOnError(TranslatableMarkup $message) {
    $this->messenger()->addError($message);
    return $this->redirect('view.user_auctions.page', ['user' => $this->currentUser()->id()]);
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    // Retrieve the accounts to be canceled from the temp store.
    /** @var \Drupal\auctions\AuctionInterface[] $auctions */
    $auctions = $this->tempStoreFactory
      ->get('auction_multiple_submit_confirm')
      ->get($this->currentUser()->id());
    if (!$auctions) {
      return $this->redirect('view.user_auctions.page', ['user' => $this->currentUser()->id()]);
    }

    $names = [];
    $fee = 0;
    $auctionsCanBeSubmitted = FALSE;
    $form['auctions'] = ['#tree' => TRUE];
    $first_auction = reset($auctions);

    // Allow bulk submit only own auctions.
    if ($first_auction->getOwnerId() != $this->currentUser()->id() && !$this->currentUser()->hasPermission('change auction state')) {
      return $this->redirectOnError($this->t('You are not allowed to bulk submit other users auctions.'));
    }

    foreach ($auctions as $auction) {
      // Solvency is calculated for single user.
      if ($first_auction->getOwnerId() != $auction->getOwnerId()) {
        return $this->redirectOnError($this->t('Only auctions from same user can be bulk submitted.'));
      }
      // Skip auctions that can not be submitted.
      if (!$auction->canBeSubmitted()) {
        continue;
      }
      $auctionsCanBeSubmitted = TRUE;
      // Build list of auction names and fee per auction.
      $id = $auction->id();
      $names[$id] = $this->t('@label (Paid)', ['@label' => $auction->label()]);
      $isPaid = $this->transactionHandler->isPaid(AUCTIONS_CREATE_AUCTION_OP, $auction);
      if (!$isPaid) {
        $auction_fee = userpoints_get_fee(AUCTIONS_CREATE_AUCTION_OP, $auction);
        $fee += $auction_fee;
        $names[$id] = $this->t('@label (Fee: @fee points)', [
          '@label' => $auction->label(),
          '@fee' => $auction_fee,
        ]);
      }
      // Build list of auctions that needs to be submitted.
      $form['auctions'][$id] = [
        '#type' => 'hidden',
        '#value' => $id,
      ];
    }

    // Bail out if no submittable auction found.
    if (!$auctionsCanBeSubmitted) {
      return $this->redirectOnError($this->t('No inactive or rejected auction selected.'));
    }

    // Add warning if auction owner has insufficient funds.
    if ($fee > 0 && !$this->transactionHandler->userIsSolventToPay($first_auction->getOwner(), $fee)) {
      $form_state->clearErrors();
      $this->messenger()->addError(t('Insufficient funds to cover activation costs. You still can submit auctions, but they will be activated once you have sufficient funds to cover cost.'));
    }

    // Set fee and owner for validation.
    $form_state->set('fee', $fee);
    $form_state->set('user', $first_auction->getOwner());

    // Build form render.
    $form['auctions']['names'] = [
      '#title' => $this->t('Auctions that will be submitted:'),
      '#theme' => 'item_list',
      '#items' => $names,
    ];
    $form['points'] = [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#value' => $this->t('Your current balance: @points', ['@points' => $this->transactionHandler->getCurrentUserBalance()]),
    ];
    $fee_msg = $fee == 0
      ? $this->t('All auctions already have been paid for.')
      : $this->t('Total fee for submitting auction: @fee', ['@fee' => $fee]);
    $form['fee'] = [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#value' => $fee_msg,
    ];

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {}

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    // Clear out the auctions from the temp store.
    $this->tempStoreFactory->get('auction_multiple_submit_confirm')->delete($this->currentUser()->id());
    // Load all auctions and set pending state if is paid or payment successful.
    if ($form_state->getValue('confirm') && !empty(array_keys($form_state->getValue('auctions')))) {
      $auctions = $this->entityTypeManager
        ->getStorage('auction')
        ->loadMultiple(array_keys($form_state->getValue('auctions')));
      /** @var \Drupal\auctions\AuctionInterface $auction */
      foreach ($auctions as $auction) {
        $auction->setPending()->save();
        $this->messenger()->addStatus($this->t('Auction %label submitted.', ['%label' => $auction->label()]));
      }
    }
    $form_state->setRedirectUrl($this->getCancelUrl());
  }

}
