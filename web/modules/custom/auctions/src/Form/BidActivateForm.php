<?php

namespace Drupal\auctions\Form;

use Drupal\Core\Entity\ContentEntityForm;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Url;

/**
 * Class BidActivateForm to activate bid.
 *
 * @package Drupal\auctions\Form
 */
class BidActivateForm extends ContentEntityForm {

  /**
   * Provides a generic edit title callback.
   *
   * @return string|null
   *   The title for the entity edit page, if an entity was found.
   */
  public function submitTitle() {
    /** @var \Drupal\auctions\BidInterface $bid */
    if ($bid = $this->getRouteMatch()->getParameter('bid')) {
      return $this->t('Activate bid for auction @auction by @owner for amount of @amount', [
        '@owner' => $bid->getOwner()->getDisplayName(),
        '@amount' => $bid->getAmount(),
        '@auction' => $bid->getAuction()->label(),
      ]);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'bid_activate_submit';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $form['message'] = [
      '#type' => 'html_tag',
      '#tag' => 'p',
      '#value' => $this->t('This action will activate bid. Bid will be considered in all future calculation.'),
    ];

    $form['actions'] = [
      '#type' => 'actions',
      'submit' => [
        '#type' => 'submit',
        '#value' => $this->t('Activate'),
      ],
      'cancel' => [
        '#title' => $this->t('Cancel'),
        '#type' => 'link',
        '#url' => $this->getCancelUrl(),
      ],
    ];

    return $form;
  }

  /**
   * Gets cancel action url.
   *
   * @return \Drupal\Core\Url
   *   Cancellation url.
   */
  public function getCancelUrl() {
    if ($destination = \Drupal::destination()->get()) {
      return Url::fromUserInput($destination);
    }
    return new Url('entity.bid.collection');
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    if ($this->entity->isEnabled()) {
      $form_state->setError($form, t('Bid already active.'));
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $this->entity->setStatus(TRUE)->save();
    $this->messenger()->addStatus($this->t('Bid activated.'));
    $form_state->setRedirectUrl($this->getCancelUrl());
  }

}
