<?php

namespace Drupal\auctions\Form;

use <PERSON><PERSON><PERSON>\Component\Datetime\TimeInterface;
use <PERSON><PERSON>al\Core\Entity\ContentEntityForm;
use Dr<PERSON>al\Core\Entity\EntityRepositoryInterface;
use <PERSON><PERSON>al\Core\Entity\EntityTypeBundleInfoInterface;
use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Messenger\MessengerInterface;
use Drupal\Core\Url;
use <PERSON><PERSON>al\userpoints\TransactionHandler;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides form to confirm auction submission.
 *
 * @package Drupal\auctions\Form
 */
class AuctionConfirmSubmitForm extends ContentEntityForm {

  /**
   * Transaction handler.
   *
   * @var \Drupal\userpoints\TransactionHandler
   */
  public $transactionHandler;

  /**
   * Constructs a AuctionConfirmSubmitForm object.
   *
   * @param \Drupal\Core\Entity\EntityRepositoryInterface $entity_repository
   *   The entity repository service.
   * @param \Drupal\userpoints\TransactionHandler $transaction_handler
   *   The transaction handler.
   * @param \Drupal\Core\Entity\EntityTypeBundleInfoInterface $entity_type_bundle_info
   *   The entity type bundle service.
   * @param \Drupal\Component\Datetime\TimeInterface $time
   *   The time service.
   */
  public function __construct(
    EntityRepositoryInterface $entity_repository,
    TransactionHandler $transaction_handler,
    ?EntityTypeBundleInfoInterface $entity_type_bundle_info = NULL,
    ?TimeInterface $time = NULL,
  ) {
    parent::__construct($entity_repository, $entity_type_bundle_info, $time);
    $this->transactionHandler = $transaction_handler;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('entity.repository'),
      $container->get('userpoints.transaction_handler'),
      $container->get('entity_type.bundle.info'),
      $container->get('datetime.time')
    );
  }

  /**
   * Provides a generic edit title callback.
   *
   * @return string|null
   *   The title for the entity edit page, if an entity was found.
   */
  public function submitTitle() {
    if ($entity = $this->getRouteMatch()->getParameter('auction')) {
      return $this->t('Submit %label auction for approval.', ['%label' => $entity->label()]);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'auctions_confirm_submit';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $this->entity->must_validate = TRUE;
    $violations = $this->entity->validate();

    foreach ($violations as $violation) {
      $this->messenger()->addError($violation->getMessage());
    }
    $repeat = !$this->entity->get('field_fees')->isEmpty() && in_array('always_auction_create_fee_25', array_column($this->entity->get('field_fees')->getValue(), 'value'));
    $fee = userpoints_get_fee(AUCTIONS_CREATE_AUCTION_OP, $this->entity);
    $isPaid = !$repeat && $this->transactionHandler->isPaid(AUCTIONS_CREATE_AUCTION_OP, $this->entity);
    if (!$isPaid && !$this->transactionHandler->userIsSolventToPay($this->entity->getOwner(), $fee)) {
      $form_state->clearErrors();
      $form_state->set('purchase', TRUE);
      $this->messenger()->addWarning(t('You have insufficient amount of points. Auction will be activated only once you have sufficient amount of points. Click submit to proceed to point purchase.'));
    }
    $isInPast = $this->entity->get('end_time')->date->getTimestamp() <= time();
    if ($isInPast) {
      $this->messenger()->addError(t('Auction end date is set in past.'));
    }
    $form['status_messages'] = [
      '#type' => 'status_messages',
    ];
    $form['message'] = [
      '#type' => 'html_tag',
      '#tag' => 'p',
      '#value' => $this->t('This action will submit the auction for approval. You will no longer be able to edit the auction (unless rejected).'),
    ];
    $form['points'] = [
      '#type' => 'html_tag',
      '#tag' => 'p',
      '#value' => $this->t('Your current balance: @points', ['@points' => $this->transactionHandler->getCurrentUserBalance()]),
    ];
    $fee_msg = $isPaid
      ? $this->t('Fee already paid.')
      : $this->t('Fee for creating auction: @fee points', ['@fee' => $fee]);
    $form['fee'] = [
      '#type' => 'html_tag',
      '#tag' => 'p',
      '#value' => $fee_msg,
    ];
    if ($this->entity->get('field_agreement')->value && $form_state->getValue('agreement') === NULL) {
      $form_state->setValue('agreement', TRUE);
    }
    $agreed = $form_state->getValue('agreement');
    $form['agreement'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('I agree to the terms of use and confirm that I do not sell the auction object in parallel on other channels and platforms.'),
      '#required' => TRUE,
      '#default_value' => $agreed,
      '#ajax' => [
        'callback' => '::ajaxCallback',
        'wrapper' => 'auction-agreement',
        'event' => 'change',
      ],
    ];
    $form['actions'] = [
      '#type' => 'actions',
    ];
    $form['actions']['submit'] = [
      '#prefix' => '<div id="auction-agreement">',
      '#suffix' => '</div>',
      '#type' => 'submit',
      '#value' => $this->t('Submit'),
      '#disabled' => $isInPast || $violations->count() || !$agreed,

    ];
    $form['#attached']['library'][] = 'auctions/agreement';
    $form['actions']['cancel'] = [
      '#title' => $this->t('Cancel'),
      '#type' => 'link',
      '#url' => $this->getCancelUrl(),
      '#attributes' => ['class' => ['button', 'button--danger']],
    ];

    return $form;
  }

  /**
   * Ajax callback for agreement checkbox.
   *
   * @param array $form
   *   Form array.
   * @param \Drupal\Core\Form\FormStateInterface $form_state
   *   Form state.
   *
   * @return array
   *   Agreement form element.
   */
  public function ajaxCallback(array $form, FormStateInterface $form_state) {
    $this->messenger()->deleteByType(MessengerInterface::TYPE_ERROR);
    return $form['actions']['submit'];
  }

  /**
   * Gets cancel action url.
   *
   * @return \Drupal\Core\Url
   *   Cancellation url.
   */
  public function getCancelUrl() {
    if ($destination = \Drupal::destination()->get()) {
      return Url::fromUserInput($destination);
    }
    return new Url('entity.auction.canonical', ['auction' => $this->entity]);
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {

    $entity = $form_state->getFormObject()->getEntity();
    $violations = $entity->validate();

    foreach ($violations as $violation) {
      /** @var \Symfony\Component\Validator\ConstraintViolationInterface $violation */
      $form_state->setErrorByName(str_replace('.', '][', $violation->getPropertyPath()), $violation->getMessage());
    }

    // The entity was validated.
    $entity->setValidationRequired(FALSE);
    $form_state->setTemporaryValue('entity_validated', TRUE);

    return $entity;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $this->messenger()->deleteByType(MessengerInterface::TYPE_ERROR);
    $this->entity->set('field_agreement', 1);
    $this->entity->setPending()->save();
    $this->messenger()->addStatus($this->t('Auction submitted for approval.'));
    if (!$form_state->get('purchase')) {
      $form_state->setRedirectUrl($this->getCancelUrl());
    }
    else {
      $this->messenger()->deleteByType(MessengerInterface::TYPE_WARNING);
      $this->messenger()->addWarning($this->t('Auction will be activated only once you have sufficient amount of points.'));
      $this->getRequest()->query->remove('destination');
      $form_state->setRedirectUrl(Url::fromRoute('userpoints.point_purchase'));
    }
  }

}
