<?php

namespace Drupal\auctions;

use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Entity\EntityViewBuilder;

/**
 * Provides a view controller for an auto bid entity type.
 */
class AutoBidViewBuilder extends EntityViewBuilder {

  /**
   * {@inheritdoc}
   */
  protected function getBuildDefaults(EntityInterface $entity, $view_mode) {
    $build = parent::getBuildDefaults($entity, $view_mode);
    // The auto bid has no entity template itself.
    unset($build['#theme']);
    return $build;
  }

}
