/**
 * @file
 * Auction edit behavior.
 */

(function (Drupal, drupalSettings, once) {

  'use strict';

  Drupal.behaviors.quantityLabelUpdate = {
    attach: function (context, settings) {
      const selectElement = context.querySelector('select[name="field_object"]');
      const labelElement = context.querySelector('.form-item-field-quantity-0-value > label');

      if (selectElement && labelElement) {
        selectElement.addEventListener('change', function () {
          const selectedValue = this.value;
          const labelMap = drupalSettings.quantityLabelMap;

          if (labelMap && labelMap[selectedValue]) {
            labelElement.textContent = labelMap[selectedValue];
          }
        });
      }
    }
  };

}(Drupal, drupalSettings, once));
