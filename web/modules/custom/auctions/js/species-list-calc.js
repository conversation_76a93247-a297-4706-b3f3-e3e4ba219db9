/**
 * @file
 * Auctions behaviors.
 */

(function ($, Drupal, drupalSettings) {
  'use strict';

  function calculatePercentage() {
    let totalSum = 0;
    $('.field--name-field-species-list .paragraphs-subform').each(function () {
      const obj = $(this).find('.field--type-decimal input');

      if (parseFloat(obj.val()) > 0) {
        totalSum += parseFloat(obj.val());
      }
    });

    $('.field--name-field-species-list .paragraphs-subform').each(function () {
      const obj = $(this).find('.field--type-decimal input');
      const species = $(this).find('.field--name-field-specie select');
      const currentValue = parseFloat(obj.val());

      if (currentValue > 0) {
        $(this)
          .find('.calc-percent')
          .text((Math.round(((100 * currentValue) / totalSum) * 100) / 100).toFixed(2) + '%');

      } else {
        $(this).find('.calc-percent').text('0.00%');
      }
      // Add event listener to check if the value is changed.
      obj.off('change').on('change', function () {
        if (parseFloat(obj.val()) > 0) {
          $('input[name="field_forest_volume[0][value]"]').trigger('change');
        }
      });
      species.on('change').on('change', function () {
        if (parseFloat(obj.val()) > 0) {
          $('input[name="field_forest_volume[0][value]"]').trigger('change');
        }
      });
    });
  }

  function addHandlers() {
    $('.field--name-field-species-list .paragraphs-subform').each(function () {
      $(this).append('<span class="form-wrapper calc-percent">0.00%</span>');
      $(this)
        .find('.field--type-decimal input')
        .change(function () {
          calculatePercentage();
        });
    });

    calculatePercentage();
  }

  if ($('.field--name-field-species-list').length > 0) {
    addHandlers();
    setInterval(function () {
      if ($('.field--name-field-species-list .paragraphs-subform .calc-percent').length === 0) {
        addHandlers();
      }
    }, 1000);
  }
})(jQuery, Drupal, drupalSettings);
