<?php

namespace Drupal\elasticsearch_handler\Plugin\ElasticsearchIndex;

use <PERSON><PERSON><PERSON>\elasticsearch_helper\Elasticsearch\Index\FieldDefinition;
use <PERSON><PERSON>al\elasticsearch_helper\Elasticsearch\Index\MappingDefinition;

/**
 * Class RegionsIndex provides index mapping for Regions taxonomy terms.
 *
 * @ElasticsearchIndex(
 *   id = "regions_index",
 *   label = @Translation("Regions Index"),
 *   entityType = "taxonomy_term",
 *   bundle = "regions",
 *   normalizerFormat = "regions",
 *   deriver = "Drupal\elasticsearch_handler\Plugin\Derivative\RegionsIndexDeriver"
 * )
 */
class RegionsIndex extends MultilingualContentIndex {

  /**
   * {@inheritdoc}
   */
  public function getMappingDefinition(array $context = []) {
    return MappingDefinition::create()
      ->addProperty('id', FieldDefinition::create('integer'))
      ->addProperty('uuid', FieldDefinition::create('keyword'))
      ->addProperty('name', FieldDefinition::create('text'))
      ->addProperty('final', FieldDefinition::create('keyword'))
      ->addProperty('location', FieldDefinition::create('geo_shape'));
  }

}
