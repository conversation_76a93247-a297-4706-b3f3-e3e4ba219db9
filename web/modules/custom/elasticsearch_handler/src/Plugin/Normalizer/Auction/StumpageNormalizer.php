<?php

namespace Drupal\elasticsearch_handler\Plugin\Normalizer\Auction;

use Drupal\elasticsearch_handler\Plugin\Normalizer\ElasticsearchHandlerAuctionNormalizerBase;

/**
 * Normalizes stumpage auctions.
 *
 * @package Drupal\elasticsearch_handler\Plugin\Normalizer\Node
 */
class StumpageNormalizer extends ElasticsearchHandlerAuctionNormalizerBase {

  /**
   * Supported formats.
   *
   * @var array
   */
  protected $format = ['stumpage'];

  /**
   * {@inheritdoc}
   */
  public function normalize($auction, $format = NULL, array $context = []): float|int|bool|\ArrayObject|array|string|null {
    $data = parent::normalize($auction, $format, $context);
    $data['administrative_area'] = $this->getTermParentIds($auction->field_administrative_area, FALSE);
    $data['cadastre_number'] = $this->getValues($auction->field_cadastre_number);
    $data['cutting_area'] = $auction->field_cutting_area->value;
    $data['cutting_type'] = $auction->field_cutting_type->value;
    $data['forest_volume'] = $auction->field_forest_volume->value;
    $data['forwarding_distance'] = $auction->field_forwarding_distance->value;
    $data['price_group'] = $auction->field_price_group->value;
    $data['property_name'] = $auction->field_property_name->value;
    $data['species_composition_keyword'] = $auction->field_species_composition->value;
    $data['species_composition_text'] = $auction->field_species_composition->value;
    $data['bids'] = count($auction->getBids());
    $data['has_bids'] = $auction->hasBids();
    $data['price_increase'] = $auction->hasBids() ? number_format(((intval($auction->getCurrentPrice()) - intval($auction->start_price->value)) / intval($auction->start_price->value)) * 100, 2) : NULL;
    $data['price_m3'] = intval($auction->field_forest_volume->value) == 0 ? NULL : round(intval($auction->getCurrentPrice()) / intval($auction->field_forest_volume->value), 2);
    $data['label'] = $auction->label();
    // Get all species from the species field.
    foreach ($auction->field_species_list->referencedEntities() as $species) {
      $data['species'][] = $species->field_specie->value;
    }
    $type = $auction->field_auction_type->value;
    if ($type == 'custom') {
      $type = $auction->field_object->value;
    }
    $data['type'] = [$type, 'any'];
    // Calculate centroid from the location from field_map.
    if (!$auction->field_map->isEmpty() && $point = \Drupal::service('geofield.geophp')->load($auction->field_map->value)?->getCentroid()) {
      $data['location'] = [
        'lat' => $point->getY(),
        'lon' => $point->getX(),
      ];
    }

    return $data;
  }

}
