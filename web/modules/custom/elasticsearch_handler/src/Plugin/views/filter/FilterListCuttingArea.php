<?php

namespace Drupal\elasticsearch_handler\Plugin\views\filter;

use <PERSON><PERSON>al\elasticsearch_handler\RangeFilterInterface;
use Drupal\views\Plugin\views\filter\InOperator;

/**
 * CProvides filter that lists auction cutting area.
 *
 * @ViewsFilter("filter_cutting_area")
 *
 * @package Drupal\elasticsearch_handler\Plugin\views\filter
 */
class FilterListCuttingArea extends InOperator implements RangeFilterInterface {

  /**
   * Cutting area ranges.
   *
   * @var array[]
   */
  const RANGES = [
    'xxs' => [
      'name' => '0-1',
      'key' => 'xxs',
      'from' => 0.01,
      'to' => 1.00,
    ],
    'xs' => [
      'name' => '1-2',
      'key' => 'xs',
      'from' => 1.01,
      'to' => 2.00,
    ],
    's' => [
      'name' => '2-3',
      'key' => 's',
      'from' => 2.01,
      'to' => 3.00,
    ],
    'm' => [
      'name' => '3-5',
      'key' => 'm',
      'from' => 3.01,
      'to' => 5.00,
    ],
    'l' => [
      'name' => '5-10',
      'key' => 'l',
      'from' => 5.01,
      'to' => 10.00,
    ],
    'xl' => [
      'name' => '10-...',
      'key' => 'xl',
      'from' => 10.01,
    ],
  ];

  /**
   * {@inheritdoc}
   */
  public static function getRanges() {
    return static::RANGES;
  }

  /**
   * {@inheritdoc}
   */
  public function getValueOptions() {
    $this->valueOptions = [];
    foreach (static::RANGES as $option) {
      $this->valueOptions[$option['key']] = $option['name'];
    }
    return $this->valueOptions;
  }

}
