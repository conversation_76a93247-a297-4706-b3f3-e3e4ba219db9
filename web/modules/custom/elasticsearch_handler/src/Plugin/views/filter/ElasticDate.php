<?php

namespace Drupal\elasticsearch_handler\Plugin\views\filter;

use <PERSON><PERSON><PERSON>\date_popup\DatePopup;

/**
 * Provides filter that handles dates.
 *
 * @ViewsFilter("elastic_date")
 *
 * @package Drupal\elasticsearch_handler\Plugin\views\filter
 */
class ElasticDate extends DatePopup {

  /**
   * Override parent method, which deals with dates as integers.
   */
  protected function opBetween($field) {}

  /**
   * Override parent method, which deals with dates as integers.
   */
  protected function opSimple($field) {}

}
