<?php

namespace Drupal\elasticsearch_handler\Plugin\Derivative;

use Drupal\Component\Plugin\Derivative\DeriverBase;
use Drupal\Core\Site\Settings;

/**
 * Creates environment specific derivatives for the news index.
 */
class NewsIndexDeriver extends DeriverBase {

  /**
   * {@inheritdoc}
   */
  public function getDerivativeDefinitions($base_plugin_definition) {
    $env = Settings::get('elasticsearch_helper.environment', 'default');
    $base_plugin_definition['indexName'] = 'news_index_{langcode}_' . $env;
    $base_plugin_definition['id'] = 'news_index:news_index_' . $env;
    $this->derivatives['news_index_' . $env] = $base_plugin_definition;
    return $this->derivatives;
  }

}
