<?php

/**
 * @file
 * Install, update and uninstall functions for the mezabirza module.
 */

use Drupal\paragraphs\Entity\Paragraph;
use Drupal\taxonomy\Entity\Term;

/**
 * Implements hook_update_N().
 *
 * Update users_field_data name field collation.
 */
function mezabirza_update_10001(): void {
  \Drupal::database()
    ->query("ALTER TABLE users_field_data MODIFY name VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;")
    ->execute();
}

/**
 * Implements hook_update_N().
 *
 * Delete all paragraph field_width values where value is full_width.
 */
function mezabirza_update_10002(): void {
  $ids = \Drupal::entityTypeManager()
    ->getStorage('paragraph')
    ->getQuery()
    ->condition('field_width', 'full_width')
    ->accessCheck(FALSE)
    ->execute();
  // Load paragraphs, set field_width to NULL and save.
  foreach (Paragraph::loadMultiple($ids) as $paragraph) {
    $paragraph->field_width = NULL;
    $paragraph->save();
  }
}

/**
 * Implements hook_update_N().
 *
 * Set Vocabulary regions field_map values.
 */
function mezabirza_update_10004(): void {
  // Create term "Iecavas pagasts" and set term 719 as its parent.
  $terms_to_create = [
    'Iecavas pagasts' => 719,
    'Ķekava' => 682,
    'Mārupe' => 681,
    'Koknese' => 716,
    'Ādaži' => 679,
  ];
  foreach ($terms_to_create as $name => $parent) {
    // Check if term already exists.
    $term = \Drupal::entityTypeManager()
      ->getStorage('taxonomy_term')
      ->loadByProperties(
        [
          'name' => $name,
          'vid' => 'regions',
        ]
      );
    if (!$term) {
      $term = \Drupal::entityTypeManager()
        ->getStorage('taxonomy_term')
        ->create(
          [
            'name' => $name,
            'vid' => 'regions',
            'parent' => $parent,
          ]
        );
      $term->save();
    }
  }
  // Create and add Pilskalnes pagasts to Aizkraukles novads.
  // Check if term exists.
  if ($terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')->loadByProperties(
      [
        'name' => 'Pilskalnes pagasts',
        'vid' => 'regions',
        'parent' => 716,
      ]
    )) {
    $pilskalnes_pagasts = reset($terms)->id();
  }
  else {
    $term = \Drupal::entityTypeManager()
      ->getStorage('taxonomy_term')
      ->create(
        [
          'name' => 'Pilskalnes pagasts',
          'vid' => 'regions',
          'parent' => 716,
        ]
      );
    $term->save();
    $pilskalnes_pagasts = $term->id();
  }
  // Delete terms under Rīga.
  $terms_to_delete = [786, 787, 788, 789, 790, 791];
  $terms_to_delete = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadMultiple($terms_to_delete);
  if (!empty($terms_to_delete)) {
    \Drupal::entityTypeManager()
      ->getStorage('taxonomy_term')
      ->delete($terms_to_delete);
  }
  $json = file_get_contents('https://lvmgeoserver.lvm.lv/geoserver/publicwfs/wfs?service=WFS&version=2.0.0&request=GetFeature&typeName=publicwfs:region&outputFormat=application/json&srsName=EPSG:4326');
  $data = json_decode($json, TRUE);
  $backend = \Drupal::service('plugin.manager.geofield_backend')->createInstance('geofield_backend_default');
  foreach ($data['features'] as $feature) {
    // Check if region name ends with u or as.
    $name = trim($feature['properties']['regionname']);
    if ($feature['properties']['regionsubtype'] == 2) {
      $name .= ' novads';
    }
    $term = \Drupal::entityTypeManager()
      ->getStorage('taxonomy_term')
      ->loadByProperties(
        [
          'name' => $name,
          'vid' => 'regions',
        ]
      );
    if ($term) {
      $term = reset($term);
      $geom = \Drupal::service('geofield.geophp')->load(json_encode($feature['geometry']));
      $term->set('field_final', $feature['properties']['regionsubtype'] == 1);
      $term->set('field_map', $backend->save($geom));
      $term->save();
      \Drupal::logger('mezabirza')->notice('Term found: @name', ['@name' => $name]);
    }
    else {
      \Drupal::logger('mezabirza')->error('Term not found: @name', ['@name' => $name]);
    }
  }

  $json = file_get_contents('https://lvmgeoserver.lvm.lv/geoserver/publicwfs/wfs?service=WFS&version=2.0.0&request=GetFeature&typeName=publicwfs:arisparish&outputFormat=application/json&srsName=EPSG:4326');
  $data = json_decode($json, TRUE);
  foreach ($data['features'] as $feature) {
    $name = trim($feature['properties']['sort_nos']);
    if ($feature['properties']['kods'] == '*********') {
      $term = [Term::load(1306)];
    }
    elseif ($feature['properties']['kods'] == '*********') {
      $term = [Term::load($pilskalnes_pagasts)];
    }
    else {
      $term = \Drupal::entityTypeManager()
        ->getStorage('taxonomy_term')
        ->loadByProperties(
          [
            'name' => $name,
            'vid' => 'regions',
          ]
        );
    }
    if ($term) {
      $term = reset($term);
      $geom = \Drupal::service('geofield.geophp')
        ->load(json_encode($feature['geometry']));
      $term->set('field_map', $backend->save($geom));
      $term->set('field_final', TRUE);
      $term->save();
      \Drupal::logger('mezabirza')
        ->notice('Term found: @name', ['@name' => $name]);
    }
    else {
      \Drupal::logger('mezabirza')
        ->error('Term not found: @name', ['@name' => $name]);
    }
  }

  $json = file_get_contents('https://lvmgeoserver.lvm.lv/geoserver/publicwfs/wfs?service=WFS&version=2.0.0&request=GetFeature&typeName=publicwfs:ariscity&outputFormat=application/json&srsName=EPSG:4326');
  $data = json_decode($json, TRUE);
  foreach ($data['features'] as $feature) {
    $name = trim($feature['properties']['sort_nos']);
    $term = \Drupal::entityTypeManager()
      ->getStorage('taxonomy_term')
      ->loadByProperties(
        [
          'name' => $name,
          'vid' => 'regions',
        ]
      );
    if ($term) {
      $term = reset($term);
      $geom = \Drupal::service('geofield.geophp')
        ->load(json_encode($feature['geometry']));
      $term->set('field_map', $backend->save($geom));
      $term->set('field_final', TRUE);
      $term->save();
      \Drupal::logger('mezabirza')
        ->notice('Term found: @name', ['@name' => $name]);
    }
    else {
      \Drupal::logger('mezabirza')
        ->error('Term not found: @name', ['@name' => $name]);
    }
  }
  // Print all terms with empty field_map.
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties(
      [
        'vid' => 'regions',
      ]
    );
  foreach ($terms as $term) {
    if ($term->get('field_map')->isEmpty()) {
      \Drupal::logger('mezabirza')
        ->notice('Term without field_map: @name', ['@name' => $term->name->value]);
    }
  }
}

/**
 * Implements hook_update_N().
 *
 * Transfer all 2nd level terms to top and delete all top level terms.
 */
function mezabirza_update_10005() {

  $term_storage = \Drupal::entityTypeManager()->getStorage('taxonomy_term');

  // Load all terms from the 'regions' vocabulary.
  $terms = $term_storage->loadTree('regions', 0, NULL, TRUE);

  // Collect all top-level term IDs.
  $top_level_term_ids = [];

  foreach ($terms as $term) {
    if (!$term->parent->target_id) {
      $top_level_term_ids[] = $term->id();
      $subscribers = \Drupal::entityTypeManager()
        ->getStorage('simplenews_subscriber')
        ->loadByProperties(['field_regions_of_interest' => $term->id()]);
      // Get $term's children.
      $child_terms = $term_storage->loadChildren($term->id());
      $children = [];
      foreach ($child_terms as $child_term) {
        $children[] = $child_term->id();
      }
      foreach ($subscribers as $subscriber) {
        // Get the subscriber's regions of interest.
        $regions_of_interest = array_column($subscriber->get('field_regions_of_interest')->getValue(), 'target_id');
        // Remove the top-level term from the regions of interest.
        $regions_of_interest = array_diff($regions_of_interest, [$term->id()]);
        // Add the children terms to the regions of interest.
        $regions_of_interest = array_merge($regions_of_interest, $children);
        // Remove duplicates.
        $regions_of_interest = array_unique($regions_of_interest);
        // Save the subscriber.
        $subscriber->set('field_regions_of_interest', $regions_of_interest);
        $subscriber->save();
      }
    }
  }

  // Set all second-level terms to top-level.
  foreach ($terms as $term) {
    if ($term->parent->target_id && in_array($term->parent->target_id, $top_level_term_ids)) {
      $term->set('parent', 0);
      $term->save();
    }
  }

  // Delete all top-level terms.
  $top_level_terms = $term_storage->loadMultiple($top_level_term_ids);
  $term_storage->delete($top_level_terms);
}

/**
 * Implements hook_update_N().
 *
 * Update all auction entities and print out all auctions that after update
 * do not have reference in field_administrative_area to regions vocabulary.
 */
function mezabirza_update_10006() {

  // Load all finished, pending and active auctions.
  $auctions = \Drupal::entityTypeManager()
    ->getStorage('auction')
    ->loadByProperties([
      'status' => ['finished', 'pending', 'active', 'declined'],
    ]);

  foreach ($auctions as $auction) {
    $geofield_value = NULL;
    if ($auction->get('field_map')->isEmpty()) {
      if ($auction->get('field_cadastre_number')->isEmpty()) {
        \Drupal::logger('mezabirza')->error('Auction without map value and cadastre number: @id', ['@id' => $auction->id()]);
        continue;
      }
      \Drupal::logger('mezabirza')->notice('Trying to get coordinates for: @id', ['@id' => $auction->id()]);
      $cadastre_number = $auction->get('field_cadastre_number')->value;
      $url = "https://lvmgeoserver.lvm.lv/geoserver/publicwfs/wfs?service=wfs&version=2.0.0&request=GetFeature&typename=publicwfs:kkparcel&outputFormat=json&srsname=EPSG:4326&cql_filter=code={$cadastre_number}";
      try {
        $response = \Drupal::httpClient()->get($url);
        $geofield_value = $response->getBody()->getContents();
        \Drupal::logger('mezabirza')->notice('Got coordinates for auction @id', ['@id' => $auction->id()]);
      }
      catch (\Exception $e) {
        \Drupal::logger('mezabirza')->error('Failed to get coordinates for auction @id', ['@id' => $auction->id()]);
      }
    }
    if (!$geofield_value) {
      $geofield_value = $auction->get('field_map')->value;
    }
    $region_search_service = \Drupal::service('elasticsearch_handler.region_search');
    $term_id = $region_search_service->searchRegions($geofield_value);
    if (is_numeric($term_id)) {
      $auction->set('field_administrative_area', $term_id);
      $auction->save();
    }
    else {
      \Drupal::logger('mezabirza')->notice('Auction without reference to regions vocabulary: @id', [
        '@id' => $auction->id(),
      ]);
    }
  }
}
