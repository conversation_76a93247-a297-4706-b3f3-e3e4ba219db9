<?php

namespace Drupal\mezabirza\Commands;

use Drupal\Core\Url;
use <PERSON>upal\message\Entity\Message;
use Drupal\user\Entity\User;
use Drush\Commands\DrushCommands;

/**
 * A Drush command file for mezabirza.
 */
class MezabirzaCommands extends DrushCommands {

  /**
   * Send campaign letters to old users.
   *
   * @param int $uid
   *   Test user id.
   *
   * @usage mezabirza-old-users-email
   *   Send campaign letters to old users.
   *
   * @command mezabirza:old-users-email
   * @aliases moue
   */
  public function mezabirzaOldUsersEmail($uid = NULL) {
    $query = \Drupal::entityQuery('user')
      ->condition('status', 1)
      ->accessCheck(FALSE);
    if (!empty($uid)) {
      $query->condition('uid', $uid);
    }
    else {
      $query->condition('access', \Drupal::time()->getCurrentTime() - 60 * 60 * 24 * 365 * 5, '<');
      $query->condition('access', \Drupal::time()->getCurrentTime() - 60 * 60 * 24 * 365 * 6, '>');
    }
    $uids = $query->execute();
    if (!empty($uids)) {
      foreach (User::loadMultiple($uids) as $user) {
        $this->sendReminderMail($user);
        if (empty($uid)) {
          $user->set('field_status', 'warning_sent')->save();
        }
      }
    }
  }

  /**
   * Send reminder mail to user.
   *
   * @param \Drupal\user\Entity\User $user
   *   User entity.
   */
  private function sendReminderMail(User $user) {
    $params = [
      'user' => $user,
    ];
    $to = $user->getEmail();
    $langcode = $user->language()->getId();
    \Drupal::service('auctions.language_switcher')->changeActiveLanguage($langcode);
    $renderer = \Drupal::service('renderer');
    $params['subject'] = t('Your profile in Meža Birža', [], ['langcode' => $langcode]);
    $params['body'][]  = t('Hi @user!', [
      '@user' => $user->getDisplayName(),
    ], ['langcode' => $langcode]);
    $params['body'][] = t('You have registered on the Meža Biržas portal, but it has been a long time since you last logged in to your profile.', [], ['langcode' => $langcode]);
    $params['body'][]  = '<br><b>' . t('Would you be interested in continuing to follow Meža Birža news and use our services?
', [], ['langcode' => $langcode]) . '</b>';
    $params['body'][] = '<ul><li>' . t('Yes, I want to continue using the site!', [], ['langcode' => $langcode]);
    $url = [
      '#type' => 'link',
      '#title' => t('Log in to your profile or reset your password.', [], ['langcode' => $langcode]),
      '#url' => Url::fromRoute('user.login')->setAbsolute(),
    ];
    $params['body'][] = '<u>' . $renderer->renderRoot($url) . '</u></li></ul>';

    $params['body'][] = '<ul><li>' . t('No, please delete my profile.', [], ['langcode' => $langcode]);
    $url = [
      '#type' => 'link',
      '#title' => t('Link to delete your profile within 24 hours.', [], ['langcode' => $langcode]),
      '#url' => Url::fromRoute('mezabirza.user.optout', [
        'user' => $user->id(),
        'token' => hash('ripemd160', $user->id() . '?auction2 mod!'),
      ])->setAbsolute(),
    ];
    $params['body'][] = '<u>' . $renderer->renderRoot($url) . '</u></li></ul>';
    $params['body'][] = '<i>' . t('Meža Birža reserves the right to delete your profile information if the profile is not used.', [], ['langcode' => $langcode]) . '</i><br>';

    $params['body'][] = '<br><b>' . t('There are several news on the Meža Birža portal:', [], ['langcode' => $langcode]) . '</b><ul>';
    $url1 = [
      '#type' => 'link',
      '#title' => t('Now you can not only sell and buy forest cuttings at the auction, but also forest properties.', [], ['langcode' => $langcode]),
      '#url' => Url::fromRoute('entity.node.canonical', [
        'node' => '559',
      ])->setAbsolute(),
    ];
    $url2 = [
      '#type' => 'link',
      '#title' => t('Improved felling price calculator. Now it is possible to compare the price with recently concluded transactions in the Meža Exchange.', [], ['langcode' => $langcode]),
      '#url' => Url::fromRoute('entity.node.canonical', [
        'node' => '564',
      ])->setAbsolute(),
    ];
    $url3 = [
      '#type' => 'link',
      '#title' => t('We publish various useful resources for forest owners.', [], ['langcode' => $langcode]),
      '#url' => Url::fromRoute('view.news_list.page', [])->setAbsolute(),
    ];
    $params['body'][] = '<li>' . $renderer->renderRoot($url1) . '</li>';
    $params['body'][] = '<li>' . $renderer->renderRoot($url2) . '</li>';
    $params['body'][] = '<li>' . $renderer->renderRoot($url3) . '</li>';

    $params['body'][] = '</ul><br>';
    $params['body'][] = t('We continue to work to improve the functionality of the Forest Exchange and help forest owners get more.', [], ['langcode' => $langcode]);
    $params['body'][] = t('You will know more - you will get more!', [], ['langcode' => $langcode]);
    \Drupal::service('plugin.manager.mail')->mail('auctions', 'account_reminder', $to, $langcode, $params, NULL, TRUE);
    // Create message entity.
    Message::create([
      'template' => 'emails',
      'uid' => $user->id(),
      'field_type' => 'account_reminder',
      'arguments' => [
        '@subject' => $params['subject'],
      ],
    ])->save();

  }

}
