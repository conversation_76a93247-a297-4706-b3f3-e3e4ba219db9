<?php

namespace <PERSON><PERSON>al\mezabirza\Form;

use <PERSON><PERSON>al\Core\DependencyInjection\ContainerInjectionInterface;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON>upal\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use Drupal\simplenews\Entity\Newsletter;
use Drupal\simplenews\Entity\Subscriber;
use Drupal\user\Entity\User;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Provides a MailSubscriptionForm form.
 */
class MailSubscriptionForm extends FormBase implements ContainerInjectionInterface {

  /**
   * Constructs a new MailSubscriptionForm object.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entityTypeManager
   *   The entity type manager.
   */
  public function __construct(protected EntityTypeManagerInterface $entityTypeManager) {}

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container): MailSubscriptionForm|static {
    return new static(
      $container->get('entity_type.manager')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'mezabirza_mail_subscription';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state, ?User $user = NULL, $token = NULL) {
    if (hash('ripemd160', $user->id() . '?MEZža mod!') != $token) {
      throw new NotFoundHttpException();
    }

    $form['#attributes']['class'][] = 'card-white';
    $form['#attached']['library'][] = 'mezabirza/deselect';

    // Create a list of newsletter options.
    $newsletter_options = [];
    foreach (Newsletter::loadMultiple() as $newsletter) {
      $newsletter_options[$newsletter->id()] = $newsletter->label();
    }
    // Load all subscriptions for the user.
    $subscriber = Subscriber::loadByUid($user->id());
    $subscriptions_list = [];
    foreach ($subscriber->getSubscribedNewsletterIds() as $subscription) {
      $subscriptions_list[] = $subscription;
    }
    // Create checkboxes for all newsletters.
    $form['newsletters'] = [
      '#type' => 'checkboxes',
      '#title' => $this->t('Newsletters'),
      '#options' => $newsletter_options,
      '#default_value' => $subscriptions_list,
    ];

    $form_state->set('entity', $user);
    $form_display = $this->entityTypeManager
      ->getStorage('entity_form_display')
      ->load('user.user.notifications');
    $form_state->set('form_display', $form_display);
    $form['#parents'] = [];

    foreach ($form_display->getComponents() as $name => $component) {
      $widget = $form_display->getRenderer($name);
      if (!$widget) {
        continue;
      }
      $items = $user->get($name);
      $items->filterEmptyItems();
      $form[$name] = $widget->form($items, $form, $form_state);
    }

    $form['actions'] = [
      '#type' => 'actions',
    ];
    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Save'),
    ];
    $form['toggle'] = [
      '#type' => 'html_tag',
      '#tag' => 'button',
      '#value' => t('Deselect all'),
      '#attributes' => [
        'id' => 'toggle-all-checkboxes',
        'class' => ['button--action'],
        'data-deselect' => 'true',
      ],
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state, ?User $user = NULL) {
    /** @var \Drupal\user\UserInterface $user */
    $user = $form_state->get('entity');
    /** @var \Drupal\simplenews\Entity\Subscriber $subscriber */
    $subscriber = Subscriber::loadByUid($user->id());
    $subscriptions = array_filter($form_state->getValue('newsletters'));
    foreach (Newsletter::loadMultiple() as $newsletter) {
      if (in_array($newsletter->id(), $subscriptions)) {
        $subscriber->subscribe($newsletter->id());
      }
      else {
        $subscriber->unsubscribe($newsletter->id());
      }
    }
    $subscriber->save();
    foreach (['field_bid_notifications', 'field_auction_notifications'] as $field_name) {
      $user->set($field_name, $form_state->getValue($field_name));
    }
    $user->save();
    $this->messenger()->addStatus($this->t('Subscription settings have been saved.'));
  }

}
