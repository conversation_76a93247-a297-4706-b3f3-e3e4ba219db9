<?php

namespace Drupal\mezabirza\Form;

use Drupal\Core\Entity\EntityFieldManagerInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Form\FormBase;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Language\LanguageManagerInterface;
use Drupal\auctions\AuctionElasticPriceCollector;
use Drupal\node\Entity\Node;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Drupal\Core\Config\ConfigFactoryInterface;

/**
 * Provides a Mežabirža form.
 */
class ShortCalculatorForm extends FormBase {


  /**
   * Node ID.
   *
   * @var int|null
   */
  private ?int $nodeId;

  /**
   * Language manager.
   *
   * @var \Drupal\Core\Language\LanguageManagerInterface
   */
  private LanguageManagerInterface $languageManager;

  /**
   * Language code.
   *
   * @var string
   */
  private string $langcode;

  /**
   * Entity field manager.
   *
   * @var \Drupal\Core\Entity\EntityFieldManagerInterface
   */
  private EntityFieldManagerInterface $entityFieldManager;

  /**
   * Price collector.
   *
   * @var \Drupal\auctions\AuctionElasticPriceCollector
   */
  private AuctionElasticPriceCollector $auctionPriceCollector;

  /**
   * Entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * Session.
   *
   * @var \Symfony\Component\HttpFoundation\Session\SessionInterface
   */
  private SessionInterface $session;

  /**
   * Config factory.
   *
   * @var \Drupal\Core\Config\ConfigFactoryInterface
   */
  protected $configFactory;

  /**
   * Price groups.
   *
   * @var array
   */
  private array $priceGroups;

  /**
   * Construct calculator form.
   *
   * @param \Drupal\Core\Language\LanguageManagerInterface $language_manager
   *   Language manager.
   * @param \Drupal\Core\Entity\EntityFieldManagerInterface $entity_field_manager
   *   Entity field manager.
   * @param \Drupal\auctions\AuctionElasticPriceCollector $auction_price_collector
   *   Price collector.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   Entity type manager.
   * @param \Symfony\Component\HttpFoundation\Session\SessionInterface $session
   *   Session.
   * @param \Drupal\Core\Config\ConfigFactoryInterface $config_factory
   *   Config factory.
   */
  public function __construct(
    LanguageManagerInterface $language_manager,
    EntityFieldManagerInterface $entity_field_manager,
    AuctionElasticPriceCollector $auction_price_collector,
    EntityTypeManagerInterface $entity_type_manager,
    SessionInterface $session,
    ConfigFactoryInterface $config_factory,
  ) {
    $this->languageManager = $language_manager;
    $this->entityFieldManager = $entity_field_manager;
    $this->auctionPriceCollector = $auction_price_collector;
    $this->entityTypeManager = $entity_type_manager;
    $this->session = $session;
    $this->configFactory = $config_factory;
    $this->langcode = $language_manager->getCurrentLanguage()->getId();
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container): CalculatorForm|static {
    return new static(
      $container->get('language_manager'),
      $container->get('entity_field.manager'),
      $container->get('auction_elastic_pricepoint'),
      $container->get('entity_type.manager'),
      $container->get('session'),
      $container->get('config.factory')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'mezabirza_calculator';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state, ?int $node_id = NULL) {
    $this->nodeId = $node_id;
    if ($this->langcode == $this->languageManager->getDefaultLanguage()->getId()) {
      $this->priceGroups = $this->entityFieldManager
        ->getFieldDefinitions('auction', 'stumpage')['field_price_group']
        ->getSetting('allowed_values');
    }
    else {
      $this->priceGroups = $this->languageManager
        ->getLanguageConfigOverride($this->langcode, 'field.storage.auction.field_price_group')
        ->get('settings')['allowed_values'];
      foreach ($this->priceGroups as $key => $value) {
        $this->priceGroups[$key] = $value['label'];
      }
    }
    // Remove option other.
    unset($this->priceGroups[4]);
    // Prepend option all.
    $this->priceGroups = [-1 => t('All')] + $this->priceGroups;
    $form['#attributes'] = [
      'class' => [
        'mezabirza-calculator-short',
      ],
    ];
    $form['input'] = [
      '#type' => 'container',
      '#attributes' => [
        'class' => ['calculator-form-input'],
      ],
    ];
    $form['input']['cadaster'] = [
      '#type' => 'container',
      '#attributes' => [
        'class' => ['calculator-form-input-cadaster'],
      ],
    ];

    // Cadastral number.
    $form['input']['cadaster']['cadastral_number'] = [
      '#type' => 'textfield',
      '#title' => t('Cadastral number'),
      '#size' => 120,
      '#default_value' => $form_state->getValue('cadastral_number') ?? '',
      '#attributes' => [
        'class' => [
          'auto_search',
        ],
      ],
    ];
    // Add error message area.
    $form['input']['error_message'] = [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#value' => '',
      '#attributes' => [
        'class' => ['error-message'],
      ],
    ];

    $form['input']['geofield'] = [
      '#type' => 'geofield_map',
      '#error_label' => t('Map'),
      '#hide_geocode_address' => TRUE,
      '#hide_coordinates' => TRUE,
      '#map_library' => 'leaflet',
      '#zoom' => [
        'start' => 7,
        'min' => 1,
        'max' => 18,
        'focus' => 7,
      ],
      '#gmap_places' => FALSE,
      '#gmap_places_options' => [],
      '#map_type' => 'OpenStreetMap_Mapnik',
      '#map_type_selector' => FALSE,
      '#map_types_google' => 'roadmap',
      '#map_types_leaflet' => [
        'OpenStreetMap_Mapnik' => [
          'label' => 'OpenStreetMap',
          'url' => 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
          'options' => [
            'maxZoom' => 19,
            'attribution' => 'Map data © <a href="https://openstreetmap.org">OpenStreetMap</a> contributors',
          ],
        ],
      ],
      '#click_to_find_marker' => FALSE,
      '#click_to_place_marker' => FALSE,
      '#click_to_remove_marker' => FALSE,
      '#geolocation' => FALSE,
      '#gmap_api_key' => '123',
      '#default_value' => [
        'lat' => '56.955436',
        'lon' => '24.241333',
      ],
      '#attributes' => [
        'class' => ['visually-hidden'],
      ],
    ];

    $form['input']['price_group'] = [
      '#type' => 'select',
      '#title' => t('Price group'),
      '#options' => $this->priceGroups,
      '#default_value' => $form_state->getValue('price_group') ?? -1,
      '#required' => TRUE,
    ];

    $form['input']['volume'] = [
      '#type' => 'number',
      '#title' => t('Volume m³'),
      '#size' => 120,
      '#min' => 50,
      '#max' => 9999,
      '#required' => TRUE,
      '#default_value' => $form_state->getValue('volume') ?? 500,
    ];

    $form['actions'] = [
      '#type' => 'actions',
      '#weight' => 100,
      'data' => [
        '#type' => 'container',
      ],
      'submit' => [
        '#type' => 'submit',
        '#value' => $this->t('Calculate'),
      ],
    ];

    $form['#attached']['library'][] = 'mezabirza/calculator_form';

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    $form_state->clearErrors();
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    // Get the values from the form in array and json_encode it.
    $values = [
      'cadastral_number' => $form_state->getValue('cadastral_number'),
      'geofield' => [
        'lat' => $form_state->getValue('geofield')['lat'],
        'lon' => $form_state->getValue('geofield')['lon'],
      ],
      'price_group' => $form_state->getValue('price_group'),
      'volume' => $form_state->getValue('volume'),
    ];
    if ($this->nodeId) {
      $url = Node::load($this->nodeId)->getUrl()->toString();
    }
    else {
      $url = '/cirsmu-cenu-kalkulators';
    }
    $url .= '?submit=true';

    // Store the data in PHP session for potential server-side use.
    $this->session->set('calculator_data', $values);

    $redirect = new RedirectResponse($url);

    $redirect->send();
  }

}
