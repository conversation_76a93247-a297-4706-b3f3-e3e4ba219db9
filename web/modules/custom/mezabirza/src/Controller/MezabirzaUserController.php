<?php

namespace <PERSON><PERSON>al\mezabirza\Controller;

use <PERSON><PERSON><PERSON>\Component\Serialization\Json;
use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON><PERSON>\Core\Routing\RouteMatchInterface;
use <PERSON><PERSON><PERSON>\Core\Url;
use <PERSON><PERSON>al\auctions\AuctionInterface;
use <PERSON><PERSON>al\commerce_order\Controller\AddressBookController;
use <PERSON><PERSON>al\profile\Entity\ProfileInterface;
use Drupal\user\UserInterface;
use Drupal\userpoints\TransactionHandler;
use Drupal\views\Views;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;

/**
 * Returns responses for Mežabirža routes.
 */
class MezabirzaUserController extends ControllerBase {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The current route match.
   *
   * @var \Drupal\Core\Routing\RouteMatchInterface
   */
  protected $routeMatch;

  /**
   * The transaction handler.
   *
   * @var \Drupal\userpoints\TransactionHandler
   */
  protected $transactionHandler;

  /**
   * The controller constructor.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Routing\RouteMatchInterface $route_match
   *   The current route match.
   * @param \Drupal\userpoints\TransactionHandler $transaction_handler
   *   The transaction handler.
   */
  public function __construct(EntityTypeManagerInterface $entity_type_manager, RouteMatchInterface $route_match, TransactionHandler $transaction_handler) {
    $this->entityTypeManager = $entity_type_manager;
    $this->routeMatch = $route_match;
    $this->transactionHandler = $transaction_handler;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('entity_type.manager'),
      $container->get('current_route_match'),
      $container->get('userpoints.transaction_handler')

    );
  }

  /**
   * Sets the given profile as default.
   *
   * @param \Drupal\profile\Entity\ProfileInterface $profile
   *   The profile.
   *
   * @return \Symfony\Component\HttpFoundation\RedirectResponse
   *   A redirect back to the overview page.
   */
  public function setDefault(ProfileInterface $profile) {
    $profile->setDefault(TRUE);
    $profile->save();
    $overview_url = Url::fromRoute('entity.user.canonical', [
      'user' => $profile->getOwnerId(),
    ]);

    return new RedirectResponse($overview_url->toString());
  }

  /**
   * Builds title.
   */
  public function userTitle() {
    return $this->t('My account');
  }

  /**
   * Builds the response.
   */
  public function build() {
    /** @var \Drupal\user\UserInterface $user */
    $user = $this->routeMatch->getParameter('user');
    $userViewBuilder = $this->entityTypeManager->getViewBuilder('user');
    $modal_options = $delete_modal_options = $add_modal_options = [
      'attributes' => [
        'class' => ['use-ajax', 'button--secondary--small'],
        'data-dialog-type' => 'modal',
        'data-dialog-options' => Json::encode([
          'minHeight' => '330',
        ]),
      ],
      'query' => [
        'destination' => \Drupal::service('path.current')->getPath(),
      ],
    ];
    $redirect_options = [
      'attributes' => [
        'class' => ['redirect', 'button--secondary--small'],
      ],
    ];
    $redirect_options_large = [
      'attributes' => [
        'class' => ['redirect', 'button--primary--small'],
      ],
    ];
    $delete_modal_options['attributes'] = [
      'class' => ['use-ajax', 'button--danger--small'],
      'data-dialog-type' => 'modal',
      'data-dialog-options' => Json::encode([]),
    ];
    $build = [
      'column-left' => [
        '#type' => 'container',
        '#attributes' => [
          'class' => ['sections__column-left'],
        ],
      ],
      'column-main' => [
        '#type' => 'container',
        '#attributes' => [
          'class' => ['sections__column-main'],
        ],
      ],
      'column-right' => [
        '#type' => 'container',
        '#attributes' => [
          'class' => ['sections__column-right'],
        ],
      ],
      '#attributes' => [
        'class' => ['sections'],
      ],
    ];

    // First column.
    $build['column-left']['profile'] = [
      '#theme' => 'card',
      '#title' => $this->t('Profile'),
      '#content' => [
        'view' => $userViewBuilder->view($user),
      ],
      '#footer' => [
        'edit-profile' => [
          '#type' => 'link',
          '#title' => $this->t('Edit'),
          '#url' => Url::fromRoute('entity.user.edit_account', ['user' => $user->id()], $modal_options),
        ],
      ],
    ];
    $my_auctions = \Drupal::database()
      ->select('auction_field_data')
      ->condition('uid', $user->id())
      ->countQuery()->execute()->fetchField();
    $won_auctions = \Drupal::database()
      ->select('auction_field_data')
      ->condition('winner', $user->id())
      ->condition('status', AuctionInterface::FINISHED)
      ->countQuery()->execute()->fetchField();
    $my_bids = \Drupal::database()
      ->select('bid')
      ->groupBy('auction')
      ->condition('uid', $user->id())
      ->countQuery()->execute()->fetchField();
    $my_followings = \Drupal::database()
      ->select('followings')
      ->condition('uid', $user->id())
      ->countQuery()->execute()->fetchField();
    $build['column-left']['auctions'] = [
      '#theme' => 'card',
      '#title' => $this->t('My auctions'),
      '#content' => [
        $this->buildViewsView('user_auctions'),
      ],
      '#footer' => [
        'following_auctions' => [
          '#type' => 'link',
          '#title' => $this->t('Followed auctions') . " ($my_followings)",
          '#url' => Url::fromRoute('view.following_auctions.page', ['user' => $user->id()], $redirect_options),
        ],
        'participated_auctions' => [
          '#type' => 'link',
          '#title' => $this->t('Participated auctions') . " ($my_bids)",
          '#url' => Url::fromRoute('view.user_auctions_participated.page', ['user' => $user->id()], $redirect_options),
        ],
        'auctions_won' => [
          '#type' => 'link',
          '#title' => $this->t('Auctions won') . " ($won_auctions)",
          '#url' => Url::fromRoute('view.user_auctions_participated.auctions_won', ['user' => $user->id()], $redirect_options),
        ],
        'my_auctions' => [
          '#type' => 'link',
          '#title' => $this->t('All of my auctions') . " ($my_auctions)",
          '#url' => Url::fromRoute('view.user_auctions.page', ['user' => $user->id()], $redirect_options_large),
        ],
      ],
    ];
    $build['column-left']['yearly_overview'] = [
      '#type' => 'link',
      '#title' => $this->t('2024 year overview 🔎'),
      '#url' => Url::fromRoute('mb_charts.user_data_charts', ['user' => $user->id()]),
      '#attributes' => [
        'class' => ['button--primary'],
        'style' => 'display: block; margin-top: 10px;',
      ],
    ];
    // Second column.
    $build['column-main']['locations'] = [
      '#theme' => 'card',
      '#title' => $this->t('Locations'),
      '#content' => [
        'view' => $userViewBuilder->view($user, 'locations'),
      ],
      '#footer' => [
        'edit-profile' => [
          '#type' => 'link',
          '#title' => $this->t('Edit'),
          '#url' => Url::fromRoute('entity.user.edit_locations', ['user' => $user->id()], $modal_options),
        ],
      ],
    ];
    $build['column-main']['notifications'] = [
      '#theme' => 'card',
      '#title' => $this->t('Notifications'),
      '#content' => [
        'view' => $userViewBuilder->view($user, 'notifications'),
      ],
      '#footer' => [
        'edit-profile' => [
          '#type' => 'link',
          '#title' => $this->t('Edit'),
          '#url' => Url::fromRoute('entity.user.edit_notifications', ['user' => $user->id()], $modal_options),
        ],
      ],
    ];
    $build['column-main']['newsletters'] = [
      '#theme' => 'card',
      '#title' => $this->t('Newsletters'),
      '#content' => [
        'view' => $userViewBuilder->view($user, 'newsletters'),
      ],
      '#footer' => [
        'edit-profile' => [
          '#type' => 'link',
          '#title' => $this->t('Edit'),
          '#url' => Url::fromRoute('simplenews.newsletter_subscriptions_user', ['user' => $user->id()], $modal_options),
        ],
      ],
    ];
    // Third column.
    $build['column-right']['points'] = [
      '#theme' => 'card',
      '#title' => $this->t('Points'),
      '#content' => [
        'view' => $this->buildPaymentView($user),
      ],
      '#footer' => [
        'invoices' => [
          '#type' => 'link',
          '#title' => $this->t('My invoices'),
          '#url' => Url::fromRoute('view.commerce_user_invoices.invoice_page', ['user' => $user->id()], $redirect_options),
        ],
        'transactions' => [
          '#type' => 'link',
          '#title' => $this->t('My transactions'),
          '#url' => Url::fromRoute('view.transactions.user', ['user' => $user->id()], $redirect_options),
        ],
        'buy' => [
          '#type' => 'container',
          'content' => [
            '#type' => 'link',
            '#title' => $this->t('Buy points'),
            '#url' => Url::fromRoute('userpoints.point_purchase', [], $redirect_options_large),
          ],
        ],
      ],
    ];
    $build['column-right']['documents'] = [
      '#theme' => 'card',
      '#title' => $this->t('Documents'),
      '#content' => [
        'view' => $userViewBuilder->view($user, 'documents'),
      ],
      '#footer' => [
        'edit-profile' => [
          '#type' => 'link',
          '#title' => $this->t('Edit'),
          '#url' => Url::fromRoute('entity.user.edit_documents', ['user' => $user->id()], $modal_options),
        ],
      ],
    ];
    $build['column-right']['billing'] = [
      '#theme' => 'card',
      '#title' => $this->t('Billing information'),
      '#content' => [
        'view' => AddressBookController::create(\Drupal::getContainer())->overviewPage($user),
      ],
    ];

    // Alter profile view mode to be more compact.
    foreach ($build['column-right']['billing']['#content']['view']['customer']['profiles'] as &$profile) {
      if (!empty($profile['profile']['#view_mode'])) {
        $profile['profile']['#view_mode'] = 'admin';
      }
      if (!empty($profile['operations']['set_default'])) {
        $profile['operations']['set_default']['#url'] = Url::fromRoute('mezabirza.address_book.set_default', [
          'user' => $profile['profile']['#profile']->getOwnerId(),
          'profile' => $profile['profile']['#profile']->id(),
        ]);
        $profile['operations']['set_default']['#attributes']['class'][] = 'button--secondary';
        $profile['operations']['edit']['#url']->setOptions($modal_options);
        $profile['operations']['delete']['#url']->setOptions($delete_modal_options);
      }
    }
    $build['column-right']['billing']['#footer']['actions']['add'] = $build['column-right']['billing']['#content']['view']['customer']['add'];
    if (!empty($build['column-right']['billing']['#footer']['actions']['add'])) {
      $add_modal_options['attributes']['class'] = [
        'use-ajax',
        'button--primary--small',
      ];
      $build['column-right']['billing']['#footer']['actions']['add']['#url']->setOptions($add_modal_options);
      $build['column-right']['billing']['#footer']['actions']['add']['#title'] = $this->t('Add billing information');
      unset($build['column-right']['billing']['#content']['view']['customer']['add']);
    }

    return $build;
  }

  /**
   * Builds payment render.
   *
   * @param \Drupal\user\UserInterface $user
   *   User.
   *
   * @return array
   *   Payment render.
   */
  protected function buildPaymentView(UserInterface $user) {
    $build = [
      [
        '#theme' => 'mb_field',
        '#label' => $this->t('Your points'),
        '#value' => $this->transactionHandler->getUserBalance($user),
        '#cache' => [
          'contexts' => [
            'user',
          ],
          'tags' => ['points_list'],
        ],
      ],
      [
        '#theme' => 'mb_field',
        '#label' => $this->t('Credit available'),
        '#value' => t('@points points', ['@points' => $this->transactionHandler->getUserCredits($user)]),
        '#cache' => [
          'contexts' => [
            'user',
          ],
          'tags' => $user->getCacheTags(),
        ],
      ],
      [
        '#theme' => 'mb_field',
        '#label' => $this->t('Account status'),
        '#value' => $user->hasRole('privileged_vip')
          ? $this->t('Privileged VIP')
          : ($user->hasRole('privileged') ? $this->t('Privileged') : $this->t('Standard')),
        '#cache' => [
          'contexts' => [
            'user',
          ],
          'tags' => $user->getCacheTags(),
        ],
      ],
    ];

    return $build;
  }

  /**
   * Builds view.
   *
   * @param string $view
   *   View name.
   * @param string $display
   *   Display name.
   *
   * @return array|null
   *   View render or nothing.
   */
  protected function buildViewsView($view, $display = 'embed') {
    $view = Views::getView($view);
    $view->setDisplay($display);
    $view->execute();
    return $view->buildRenderable();
  }

}
