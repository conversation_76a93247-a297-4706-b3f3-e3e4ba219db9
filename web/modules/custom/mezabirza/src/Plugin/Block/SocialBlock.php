<?php

namespace Drupal\mezabirza\Plugin\Block;

use Drupal\Core\Block\BlockBase;
use <PERSON>upal\Core\Form\FormStateInterface;

/**
 * Provides a social block.
 *
 * @Block(
 *   id = "mezabirza_social",
 *   admin_label = @Translation("Social"),
 *   category = @Translation("Custom")
 * )
 */
class SocialBlock extends BlockBase {

  /**
   * {@inheritdoc}
   */
  public function blockSubmit($form, FormStateInterface $form_state) {
    $this->configuration['text'] = $form_state->getValue('text')['value'];
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    $build['social_links'] = [
      [
        'url' => 'https://www.facebook.com/mezabirzalv',
        'title' => 'Facebook',
        'icon' => 'facebook',
      ],
      [
        'url' => 'https://www.youtube.com/@MezaBirza',
        'title' => 'Youtube',
        'icon' => 'youtube',
      ],
      [
        'url' => 'https://x.com/MezaBirza',
        'title' => 'X (Twitter)',
        'icon' => 'x',
      ],
      [
        'url' => 'https://www.tiktok.com/@meza.birza',
        'title' => 'TikTok',
        'icon' => 'tiktok',
      ],
    ];

    return $build;
  }

}
