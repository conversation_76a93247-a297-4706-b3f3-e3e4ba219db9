<?php

declare(strict_types=1);

namespace <PERSON><PERSON>al\mezabirza\Plugin\Block;

use <PERSON><PERSON>al\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON><PERSON><PERSON>\Core\Routing\RouteMatchInterface;
use <PERSON><PERSON><PERSON>\node\NodeInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides a hero title block.
 *
 * @Block(
 *   id = "mezabirza_hero_title",
 *   admin_label = @Translation("Hero Title"),
 *   category = @Translation("Custom"),
 * )
 */
final class HeroTitleBlock extends BlockBase implements ContainerFactoryPluginInterface {

  /**
   * Constructs the plugin instance.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    private readonly RouteMatchInterface $routeMatch,
    private readonly EntityTypeManagerInterface $entityTypeManager,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition): self {
    return new self(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('current_route_match'),
      $container->get('entity_type.manager')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function build(): array {
    $build = [];
    $node = $this->routeMatch->getParameter('node');
    $build['#cache']['tags'] = ['node_list:page'];
    $build['#cache']['contexts'][] = 'url.path';
    if ($node instanceof NodeInterface && $node->hasField('field_hero') && !$node->get('field_hero')->isEmpty()) {
      // Render first paragraph of the field_hero field.
      /** @var \Drupal\paragraphs\Entity\Paragraph $hero **/
      $hero = $node->get('field_hero')->referencedEntities()[0];
      // Get view builder.
      $build[] = $this->entityTypeManager->getViewBuilder('paragraph')->view($hero, 'hero');
    }
    return $build;
  }

}
