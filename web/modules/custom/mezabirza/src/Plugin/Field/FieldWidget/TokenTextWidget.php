<?php

namespace Drupal\mezabirza\Plugin\Field\FieldWidget;

use <PERSON>upal\Core\Field\FieldItemListInterface;
use Drupal\Core\Field\Plugin\Field\FieldWidget\StringTextfieldWidget;
use Drupal\Core\Form\FormStateInterface;

/**
 * Plugin implementation of the 'token_text' widget.
 *
 * @FieldWidget(
 *   id = "token_text",
 *   label = @Translation("Text with Token Selector"),
 *   field_types = {
 *     "string"
 *   }
 * )
 */
class TokenTextWidget extends StringTextfieldWidget {

  /**
   * {@inheritdoc}
   */
  public static function defaultSettings() {
    return [
      'token_types' => ['node', 'paragraph'],
    ] + parent::defaultSettings();
  }

  /**
   * {@inheritdoc}
   */
  public function formElement(FieldItemListInterface $items, $delta, array $element, array &$form, FormStateInterface $form_state) {
    $element = parent::formElement($items, $delta, $element, $form, $form_state);
    $element['token_tree'] = [
      '#theme' => 'token_tree_link',
      '#token_types' => ['user', 'auction'],
      '#show_restricted' => TRUE,
    ];

    return $element;
  }

}
