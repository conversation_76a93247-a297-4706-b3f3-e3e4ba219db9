mezabirza.address_book.set_default:
  path: '/user/{user}/payment-info/{profile}/set-default'
  defaults:
    _controller: '\Drupal\mezabirza\Controller\MezabirzaUserController::setDefault'
  options:
    parameters:
      user:
        type: 'entity:user'
      profile:
        type: 'entity:profile'
  requirements:
    _address_book_access: 'TRUE'
    _entity_access: 'profile.update'
    profile: '\d+'
    _csrf_token: 'TRUE'

entity.user.edit_region:
  path: '/user/{user}/edit-region'
  defaults:
    _entity_form: 'user.region'
    _title_callback: 'Drupal\user\Controller\UserController::userTitle'
  requirements:
    user: '\d+'
    _entity_access: 'user.update'
entity.user.edit_notifications:
  path: '/user/{user}/edit-notifications'
  defaults:
    _entity_form: 'user.notifications'
    _title_callback: 'Drupal\user\Controller\UserController::userTitle'
  requirements:
    user: '\d+'
    _entity_access: 'user.update'
entity.user.edit_locations:
  path: '/user/{user}/edit-locations'
  defaults:
    _entity_form: 'user.locations'
    _title_callback: '<PERSON>upal\user\Controller\UserController::userTitle'
  requirements:
    user: '\d+'
    _entity_access: 'user.update'
entity.user.edit_documents:
  path: '/user/{user}/edit-documents'
  defaults:
    _entity_form: 'user.documents'
    _title_callback: 'Drupal\user\Controller\UserController::userTitle'
  requirements:
    user: '\d+'
    _entity_access: 'user.update'
entity.user.edit_account:
  path: '/user/{user}/edit-account'
  defaults:
    _entity_form: 'user.account'
    _title_callback: 'Drupal\user\Controller\UserController::userTitle'
  requirements:
    user: '\d+'
    _entity_access: 'user.update'
entity.user.edit_subscriptions:
  path: '/user/{user}/edit-subscriptions'
  defaults:
    _entity_form: 'user.subscriptions'
    _title_callback: 'Drupal\user\Controller\UserController::userTitle'
  requirements:
    user: '\d+'
    _entity_access: 'user.update'

mezabirza.invoice_regenerate:
  path: '/invoice/{commerce_invoice}/regenerate'
  defaults:
    _title: 'Invoice regenerate'
    _form: 'Drupal\mezabirza\Form\InvoiceRegenerateForm'
  requirements:
    commerce_invoice: '\d+'
    _entity_access: 'commerce_invoice.update'

mezabirza.admin_settings:
  path: '/admin/config/mezabirza/settings'
  defaults:
    _form: '\Drupal\mezabirza\Form\MezabirzaSettingsForm'
    _title: 'Mezabirza settings'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE

mezabirza.user.optout:
  path: '/user/opt-out/{user}/{token}'
  defaults:
    _title: 'Opt out'
    _controller: '\Drupal\mezabirza\Controller\MezabirzaUserOptOutController::build'
  requirements:
    user: '\d+'
    token: '\w+'
    _permission: 'access content'

mezabirza.single_email:
  path: '/admin/content/single-email'
  defaults:
    _title: 'Single email'
    _form: 'Drupal\mezabirza\Form\SingleEmailForm'
  requirements:
    _permission: 'administer newsletters'

mezabirza.mail_subscription:
  path: '/mail-subscription/{user}/{token}'
  defaults:
    _title: 'Mail subscription'
    _form: 'Drupal\mezabirza\Form\MailSubscriptionForm'
  requirements:
    user: '\d+'
    token: '\w+'
    _permission: 'access content'

mezabirza.default_image:
  path: '/admin/config/mezabirza/default-image'
  defaults:
    _title: 'Default Image'
    _form: 'Drupal\mezabirza\Form\DefaultImageForm'
  requirements:
    _permission: 'access administration pages'
