<?php

/**
 * @file
 * Show registered user email address in profile page.
 */

use <PERSON><PERSON>al\Core\Routing\RouteMatchInterface;

/**
 * Implements hook_help().
 */
function show_email_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.show_email':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('Show registered user email address in profile page.') . '</p>';
      $output .= '<p>' . t('You can configure Show Email settings <b>Configuration » People » Account settings » Manage display</b>.');
      return $output;
  }
}

/**
 * Implements hook_entity_base_field_info_alter().
 */
function show_email_entity_base_field_info_alter(&$fields, $entity_type) {
  // If entity is user show email address.
  if ($entity_type->id() == 'user') {
    if (isset($fields['mail'])) {
      $fields['mail']->setDisplayConfigurable('view', TRUE)
        ->setTargetEntityTypeId('user');
    }
  }
}
