<?php

namespace Drupal\userpoints;

use <PERSON><PERSON>al\Core\Entity\EntityInterface;
use Drupal\Core\Entity\EntityListBuilder;
use <PERSON>upal\Core\Link;

/**
 * Defines a class to build a listing of Transaction entities.
 *
 * @ingroup userpoints
 */
class TransactionListBuilder extends EntityListBuilder {

  /**
   * {@inheritdoc}
   */
  public function buildHeader() {
    $header['id'] = $this->t('Transaction ID');
    $header['name'] = $this->t('Name');
    return $header + parent::buildHeader();
  }

  /**
   * {@inheritdoc}
   */
  public function buildRow(EntityInterface $entity) {
    /** @var \Drupal\userpoints\Entity\Transaction $entity */
    $row['id'] = $entity->id();
    $row['name'] = Link::createFromRoute(
      $entity->label(),
      'entity.transaction.edit_form',
      ['transaction' => $entity->id()]
    );
    return $row + parent::buildRow($entity);
  }

}
