<?php

namespace Drupal\userpoints\Plugin\Commerce\Condition;

use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Form\FormStateInterface;
use Drupal\commerce\Plugin\Commerce\Condition\ConditionBase;

/**
 * Provides the Email address condition for orders.
 *
 * @CommerceCondition(
 *   id = "order_legal_status",
 *   label = @Translation("Customer legal status"),
 *   category = @Translation("Customer"),
 *   entity_type = "commerce_order",
 * )
 */
class OrderLegalStatus extends ConditionBase {

  /**
   * {@inheritdoc}
   */
  public function defaultConfiguration() {
    return [
      'legal_status' => NULL,
    ] + parent::defaultConfiguration();
  }

  /**
   * {@inheritdoc}
   */
  public function buildConfigurationForm(array $form, FormStateInterface $form_state) {
    $form = parent::buildConfigurationForm($form, $form_state);

    $form['legal_status'] = [
      '#type' => 'select',
      '#title' => $this->t('Legal status'),
      '#options' => [
        'legal_entity' => $this->t('Legal entity'),
        'individual' => $this->t('Individual'),
      ],
      '#default_value' => $this->configuration['legal_status'],
      '#required' => TRUE,
    ];
    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function submitConfigurationForm(array &$form, FormStateInterface $form_state) {
    parent::submitConfigurationForm($form, $form_state);

    $values = $form_state->getValue($form['#parents']);
    $this->configuration['legal_status'] = $values['legal_status'];
  }

  /**
   * {@inheritdoc}
   */
  public function evaluate(EntityInterface $entity) {
    $this->assertEntity($entity);
    /** @var \Drupal\commerce_order\Entity\OrderInterface $order */
    $profile = $entity->getBillingProfile();
    return !$profile?->field_legal_status?->isEmpty() && $this->configuration['legal_status'] === $profile?->field_legal_status?->value;
  }

}
