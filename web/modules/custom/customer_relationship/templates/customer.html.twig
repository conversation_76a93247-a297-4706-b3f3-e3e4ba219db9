{#
/**
 * @file
 * Default theme implementation to present a customer entity.
 *
 * This template is used when viewing a registered customer's page,
 * e.g., /admin/content/customers)/123. 123 being the customer's ID.
 *
 * Available variables:
 * - content: A list of content items. Use 'content' to print all content, or
 *   print a subset such as 'content.title'.
 * - attributes: HTML attributes for the container element.
 *
 * @see template_preprocess_customer()
 */
#}
<article{{ attributes }}>
  {% if content %}
    {{- content -}}
  {% endif %}
</article>
