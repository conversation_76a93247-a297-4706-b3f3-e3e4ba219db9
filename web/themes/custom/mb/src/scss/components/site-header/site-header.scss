.header {
  background-color: $color--primary-background;
  position: relative;
  @extend .shadow;

  &__inner {
    @include wrapper;

    display: flex;
    flex-flow: column nowrap;
  }

  &__primary {
    position: relative;
    z-index: 100;
    .inner {
      @include wrapper;
      display: flex;
      flex-flow: row wrap;
      justify-content: space-between;
      @include from-s {
        flex-flow: row wrap;
      }
    }
    @include from-s {
      padding: space();
      z-index: 100;
    }
  }

  &__secondary {
    color: $color--default-text;
    background-color: $color--default-background;
    box-shadow: 0 -1px 3px #00000045;
    position: fixed;
    width: 100%;
    bottom: 0;
    z-index: 100;
    @include from-s {
      color: $color--default-text;
      background-color: $color--default-background;
      position: relative;
      z-index: 99;
    }
    .inner {
      @include wrapper;
      padding-left: .5rem;
      padding-right: .5rem;
    }
    .action-link.secondary {
      background: transparent;
      font-weight: 500;
      .action-link__icon {
        fill: $color--default-text;
        stroke: $color--default-text;
      }
    }
  }

  &__info {
    padding: 0;
    background-color: $color--secondary-background;
    box-shadow: 0 -1px 3px #00000045;
    .inner {
      @include wrapper;
      padding-left: .5rem;
      padding-right: .5rem;
    }
    div {
      display: flex;
      justify-content: flex-end;
      width: 100%;
      @include from-s {
        width: unset;
      }
    }
  }

  &__branding {
    margin-right: $space;
    max-width: space(12);
    display: flex;
    align-items: center;
  }
}
