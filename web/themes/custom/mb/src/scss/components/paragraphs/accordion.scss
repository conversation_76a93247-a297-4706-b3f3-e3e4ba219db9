.width-wide {
  .accordion-item {
    @include wrapper-width($width-content--image-hero);
    @include wrapper-gutter;
    margin-bottom: 1rem;
  }
}

.paragraph.accordion-item.paragraph-accordion-item {
  margin-bottom: 1rem;
  background: transparent;
  .field--name-field-image {
    max-width: 26px;
    align-self: center;
    img {
      max-height: 26px;
      object-fit: contain;
      max-width: unset;
    }
  }
  .paragraph__content {
    background: transparent;
  }
}
