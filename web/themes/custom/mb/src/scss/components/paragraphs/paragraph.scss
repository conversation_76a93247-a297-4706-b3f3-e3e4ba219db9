a.green,
.green,
.green details,
.green summary {
  background: #164F30;
  color: $color--default-text-negative;

  a:not(.button) {
    color: $color--default-text-negative;
    :hover {
      color: $color--meta-background;
    }
  }
  :hover:not(.button) {
    color: $color--meta-background;
  }
}
a.green:hover, .green:hover, .green:hover details, .green:hover summary {
  color: $color--meta-background;
}
.green.lightest, a.green.lightest, .green.lightest details, .green.lightest summary {
  background: #1B673E;
}
.green.light, a.green.light, .green.light details, .green.light summary {
  background: #175A37;
}
.green.dark, a.green.dark, .green.dark details, .green.dark summary {
  background: #144429;
}
.green.darkest, a.green.darkest, .green.darkest details, .green.darkest summary {
  background: #103923;
}
.green.brown-green, a.green.brown-green, .green.brown-green details, .green.brown-green summary {
  background: linear-gradient(45deg, $color--footer-background, $color--primary);
}
.green.green-brown, a.green.green-brown, .green.green-brown details, .green.green-brown summary {
  background: linear-gradient(45deg, $color--primary, $color--footer-background);
}
.green.brown, a.green.brown, .green.brown details, .green.brown summary {
  background: $color--footer-background;
}
.primary, .primary details, .primary summary {
  background: $color--primary-background;
  color: $color--default-text-negative;
  a {
    color: $color--default-text-negative;
    :hover {
      color: $color--meta-background;
    }
  }
}
.secondary, .secondary details, .secondary summary {
  background: $color--secondary-background;
  color: $color--default-text-negative;
  a {
    color: $color--default-text-negative;
    :hover {
      color: $color--meta-background;
    }
  }
}

.paragraph h2 {
  text-align: center;
}


.paragraph__content > h2 {
  margin-top: 0;
}

.width-wide {
  .paragraph {
    &.paragraph--width--page-hero-width {
      @include wrapper-width($width-content--image-hero);
    }

    &.paragraph--width--narrow-page-width {
      @include wrapper-width($width-content--image);
    }
  }
}
