/////////////////////////////

button,
button.link,
.button {
  @include button--primary;;
  &.toolbar-handle {
    margin: 0;
  }
  &.is-disabled {
    background-color: $color--gray;
    cursor: not-allowed;
  }

  &--primary {
    @include button--primary;
    &--small {
      @include button--primary;
      font-size: 1.4rem;
    }
    &--large {
      @include button--primary;
      font-size: 2.2rem;
    }
  }

  &--action {
    @include button--action;
    @extend .shadow-md;

    svg {
      width: 20px;
      height: 20px;
    }
    &--small {
      @include button--action;
      @extend .shadow-md;

      svg {
        width: 20px;
        height: 20px;
      }
      font-size: 1.4rem;
    }
    &--large {
      @include button--action;
      @extend .shadow-md;

      svg {
        width: 24px;
        height: 24px;
      }
      font-size: 2.2rem;
    }
  }

  &--cta {
    @include button--cta;
    &--small {
      @include button--cta--small;
    }
    &--large {
      @include button--cta;
      font-size: 2.2rem;
    }
  }

  &--secondary {
    @include button--secondary;
    &--small {
      @include button--secondary--small;
      font-size: 1.4rem;
    }
    &--large {
      @include button--secondary;
      font-size: 2.2rem;
    }
  }

  &--clean {
    @include button--clean;
    &--small {
      @include button--clean--small;
    }
    &--large {
      @include button--clean;
      font-size: 2.2rem;
    }
  }

  &--auto {
    @include button--auto;
    &--small {
      @include button--auto--small;
    }
    &--large {
      @include button--auto;
      font-size: 2.2rem;
    }
  }

  &--outline {
    @include button--outline;
    &--small {
      @include button--outline--small;
    }
    &--large {
      @include button--outline;
      font-size: 2.2rem;
    }
  }

  &--danger {
    @include button--danger;
    &--small {
      @include button--danger;
      font-size: 1.4rem;
    }
    &--large {
      @include button--danger;
      font-size: 2.2rem;
    }
  }

  &--follow {
    use {
      fill: $color--default-text-negative;
      stroke: $color--default-text;
    }

    &.following {
      use {
        fill: $color--default-text;
      }
    }
    &--small {
      @include button--primary;
      font-size: 1.4rem;
    }
    &--large {
      @include button--primary;
      font-size: 2.2rem;
    }
  }
}

.add-another,
button.tableresponsive-toggle {
  @include button--secondary;
}

.close {
  position: relative;
  width: 32px;
  height: 32px;
  &::before,
  &::after {
    position: absolute;
    left: 15px;
    content: ' ';
    height: 33px;
    width: 2px;
    background-color: #333;
  }
  &::before {
    transform: rotate(45deg);
  }
  &::after {
    transform: rotate(-45deg);
  }
}

@mixin button--bar {
  display: inline-block;
  margin: 0;
  padding-left: .5rem;
  list-style: none;
  background-color: #f5f5f5;
  border-radius: $border-radius--default;
  li {
    display: inline-block;

    a {
      @include button--secondary;
    }
    a.is-active {
      background-color: $color--primary-light;
      color: white;
    }
  }
}

@mixin cross($size: 20px, $color: currentColor, $thickness: 1px) {
  margin: 0;
  padding: 0;
  border: 0;
  background: none;
  position: relative;
  width: $size;
  height: $size;
  &::before,
  &::after {
    content: '';
    position: absolute;
    top: calc(($size - $thickness) / 2);
    left: 0;
    right: 0;
    height: $thickness;
    background: $color;
    border-radius: $thickness;
  }

  &::before {
    transform: rotate(45deg);
  }

  &::after {
    transform: rotate(-45deg);
  }

  &:hover {
    cursor: pointer;
    &::before,
    &::after {
      background: darken($color, 50%);
    }
  }



  span {
    display: block;
  }

}

.cross-stand-alone {
  display: inline-block;
  @include cross(20px, #2d3236, 2px);
}
