form.auction-stumpage-add-form,
form.auction-stumpage-edit-form {
  .auction-type-wrapper {
    justify-content: space-between;
    .field--name-field-other-participants,
    .field--name-field-participants {
      //flex: unset;
    }
    .field--name-field-private {
      flex: 100%;
    }
  }
}

.field--name-field-participants {
  @include from-s {
    display: flex;
  }
  fieldset {
    min-width: 100%;
  }
  .search-element__input {
    margin-bottom: space(1);
    &>input {
      display: block;
      height: 3.6rem;
      border: $form-el-border-size solid $color--form-el-border;
      padding: 0.5em;
      font-size: 1.5rem;
      border-radius: $form-el-border-radius;
      width: 100%;

      &:focus {
        border-color: $color--form-el-checked !important;
        outline: 0;
      }

      &::-webkit-input-placeholder {
        color: $color--default-text !important;
      }

      &:-moz-placeholder {
        color: $color--default-text !important;
      }

      &::-moz-placeholder {
        color: $color--default-text !important;
      }

      &:-ms-input-placeholder {
        color: $color--default-text !important;
      }

      &.error {
        border: $form-el-border-size solid $color--error-dark;
      }
    }
  }

  .content {
    height: 30rem;
    overflow-y: scroll;
    padding: 1rem;
    border: $form-el-border-size solid $color--form-el-border;

    .form-item--checkboxes {
      .form-type--checkbox {
        padding: 1rem;
        margin: 0;
        .form-item__label::before, .form-item__label::after {
          top: unset;
          left: 5px;
        }
      }
    }
  }
}
