
.page-user-transactions {
  .block--main-block {
    @extend .card-white;
  }
}

.page-user-auctions,
.page-user-profile {
  .block--main-block {
    > .form-fieldset {
      > .content {
        @extend .card-white;
      }
      legend {
        @extend .heading-section;
      }
    }
  }
}

.profile-form,
.user-form {
  .form-fieldset {
    legend {
      display: inline-block;
      width: 100%;
      font-size: 2rem;
    }
  }
  .form-item {
    input[type="text"],
    input[type="tel"],
    input[type="password"] {
      width: 100%;
    }
  }
  .address-container-inline > .form-item {
    margin: 0 0 $space 0;
    width: 100%;
  }
}

/**
 * @file
 * Module styling for user module.
 */
.password-confirm,
.password-suggestions,
.password-strength__title,
.password-strength__text {
  font-size: 13px;
  color: $color--secondary-text;
}
.password-strength__title,
.password-strength__text {
  display: inline;
}
.password-strength__meter {
  height: 8px;
  margin-top: 0.5em;
  background-color: rgba(255,255,255,0.3);
  border-radius: 3px;
}
.password-strength__indicator {
  width: 0;
  height: 100%;
  background-color: $color--primary-light;
  border-radius: 3px;
}
.password-confirm-match {
  visibility: hidden;
}
.password-confirm {
  span.error {
    color: $color--error;
  }
  span.ok {
    color: $color--primary-light;
  }
}
.password-suggestions {
  ul {
    margin: 0;
    padding-left: 2em;
  }
}

.page-user-edit {
  .user-form {
    @extend .card-white;
  }
}
