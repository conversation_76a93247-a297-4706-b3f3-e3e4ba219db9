@mixin transition($transition-property, $transition-time, $method) {
  -webkit-transition: $transition-property $transition-time $method;
  -moz-transition: $transition-property $transition-time $method;
  -ms-transition: $transition-property $transition-time $method;
  -o-transition: $transition-property $transition-time $method;
  transition: $transition-property $transition-time $method;
}

@mixin transition-default($properties: (all)) {
  $property-string: '';

  @each $property in $properties {
    @if $property-string != '' {
      $property-string: #{$property-string}, #{$property} 0.15s;
    }
    @if $property-string == '' {
      $property-string: #{$property} 0.1s ease-out;
    }
  }

  transition: $property-string;

  @content;
}
