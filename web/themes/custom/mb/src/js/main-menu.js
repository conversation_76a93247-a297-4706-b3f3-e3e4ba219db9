(function (Drupal, $, once) {
Drupal.behaviors.mainMenu = {
  attach(context) {
    const toggleExpand = context.getElementsByClassName('mobile-menu-button')[0];
    const menu = context.getElementsByClassName('header__menu')[0];
    if (menu) {
      const expandMenu = menu.getElementsByClassName('chevron');

      // Mobile Menu Show/Hide.
      once('menu-open-behaviour', '.mobile-menu-button', context).forEach((toggleExpand) => {
        toggleExpand.addEventListener('click', e => {
          toggleExpand.classList.toggle('opened');
          menu.classList.toggle('opened');
          e.preventDefault();
        });
      });

      // Expose mobile sub menu on click and enter key press.
      for (let i = 0; i < expandMenu.length; i += 1) {
        const handleExpand = e => {
          if (e.type === 'click' || (e.type === 'keydown' && e.key === 'Enter')) {
            // Close all main-menu__item--with-sub opened.
            const expandSub = context.getElementsByClassName('expand-sub--opened');
            for (let j = 0; j < expandSub.length; j += 1) {
              // check if the current element is the same as the clicked element.
              // if so, do not close it.
              if (expandSub[j].contains(e.currentTarget)) {
                continue;
              }
              expandSub[j].classList.remove('expand-sub--opened');
            }

            const menuItem = e.currentTarget;
            // Get parent and toggle class expand-sub--opened.
            const parent = menuItem.parentElement;
            parent.classList.toggle('expand-sub--opened');
          }
        };

        expandMenu[i].addEventListener('click', handleExpand);
        expandMenu[i].addEventListener('keydown', handleExpand);
      }

      // Clicking outside the menu will remove expand-sub--opened class.
      document.addEventListener('click', (e) => {
        const isClickInside = menu.contains(e.target);
        if (!isClickInside) {
          const expandSub = context.getElementsByClassName('expand-sub--opened');
          for (let j = 0; j < expandSub.length; j += 1) {
            expandSub[j].classList.remove('expand-sub--opened');
          }
        }
      });

    }
  },
};
} (Drupal, jQuery, once));

