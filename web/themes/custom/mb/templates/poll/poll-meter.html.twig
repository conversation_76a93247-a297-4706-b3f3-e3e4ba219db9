{#
/**
 * @file
 * Default theme implementation for a meter.
 *
 * Available variables:
 * - display_value: The textual representation of the meter bar.
 * - form: One or more forms to which the <meter> element belongs; multiple
 *   forms separated by spaces.
 * - high: A number specifying the range that is considered to be a high value.
 * - low: A number specifying the range that is considered to be a low value.
 * - max: A number specifying the maximum value of the range.
 * - min: A number specifying the minimum value of the range.
 * - optimum: A number specifying what value is the optimal value for the gauge.
 * - value: A number specifying the current value of the gauge.
 * - percentage: A number specifying the current percentage of the gauge.
 * - attributes: HTML attributes for the containing element.
 * - choice: The choice of a poll.
 *
 * @see template_preprocess()
 * @see template_preprocess_region()
 *
 * @ingroup themeable
 */
#}
{% set is_current_selection_class = is_current_selection ? 'is-current-selection' : 'not-current-selection' %}
<dt class="choice-title {{ is_current_selection_class }}">
  {{ choice }}
  {% if display_value %}
    <span class="percent">(
      {{ percentage }}%,
      {% trans %}
        {{ value }} Vote
      {% plural value %}
        {{ value }} Votes
      {% endtrans %}
      )</span>
  {% endif %}
</dt>
<dd class="choice-result {{ is_current_selection_class }}">
  <div{{ attributes }}>
    <div style="width: {{ percentage }}%" class="foreground"></div>
  </div>
</dd>
