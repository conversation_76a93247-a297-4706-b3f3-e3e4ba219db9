{#
This is a partial template used in multiple auction templates.
See display modes and auction module preprocess for more details.
#}

{% if content.description.0 %}
  <div class="section section__description">
    <h2 class="section-title">{{ "Description"|t }}</h2>
    {{ content.description }}
  </div>
{% endif %}
{% if show_attachments %}
  <div class="section section__attachments">
    <h2 class="section-title">{{ "Attachments"|t }}</h2>
    {% if not logged_in %}
      <div class="section-title">
        <a href="{{ path('user.login', {'destination':path('<current>')}) }}">{{ "Log in to see attachments"|t }}</a>
      </div>
    {% else %}
      {{ content.field_attachments }}
    {% endif %}
  </div>
{% endif %}
{% if content.field_images.0 or content.field_360_images.0 %}
  <div class="section section__images">
    <h2 class="section-title">{{ "Images"|t }}</h2>
    {% if not logged_in and not show_attachments_unauthorised %}
      <div class="section-title">
        <a href="{{ path('user.login', {'destination':path('<current>')}) }}">{{ "Log in to see images"|t }}</a>
      </div>
    {% else %}
      {{ content.field_images }}
      {{ content.field_360_images }}
    {% endif %}
  </div>
{% endif %}
{% if content.field_video.0 %}
  <div class="section section__video">
    <h2 class="section-title">{{ "Video"|t }}</h2>
    {% if not logged_in and not show_attachments_unauthorised %}
      <div class="section-title">
        <a href="{{ path('user.login', {'destination':path('<current>')}) }}">{{ "Log in to see video"|t }}</a>
      </div>
    {% else %}
      {{ content.field_video }}
    {% endif %}
  </div>
{% endif %}
