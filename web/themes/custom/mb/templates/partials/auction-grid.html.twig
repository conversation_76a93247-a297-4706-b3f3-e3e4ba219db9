{#
/**
 * @file
 * Theme override for an grid.
 *
 * Available variables:
 * - items: A list of items. Each item contains:
 *   - attributes: HTML attributes to be applied to each list item.
 *   - value: The content of the list element.
 * - title: The title of the list.
 */
#}
{#
/**
 * Available variables:
 * - grid_base_class - the base classname
 * - grid_modifiers - array of modifiers to add to the base classname
 *
 * - items - array of grid items. Their type must correspond with the "grid_type" value
 */
#}
{% set grid__base_class = grid__base_class|default('grid') %}
{% set grid__modifiers = grid__modifiers|default([]) %}

{% set show_more = 'show-more' in grid__modifiers %}
{% set last_item_in_column = 'three-columns' in grid_modifiers ? 3 : 4 %}

{% if 'three-columns' not in grid__modifiers %}
  {% set grid__modifiers = grid__modifiers|merge(['four-columns']) %}
{% endif %}

{% if not show_items %}
  {% set show_items = last_item_in_column %}
{% endif %}


<div {{ bem(grid__base_class, grid__modifiers) }}>

  {% if label %}
    <h3 {{ bem('title', [], grid__base_class) }}>{{ label }}</h3>
  {% endif %}
  {% block grid_content %}

    <div {{ bem('items', ['visible'], grid__base_class) }}>
      {% if show_more %}
        {% for item in items|slice(0, show_items) %}
          {{ item }}
        {% endfor %}
      {% else %}
        {% for item in items %}
          {{ item }}
        {% endfor %}
      {% endif %}
    </div>

    {% if show_more and items|length > show_items %}
      <div {{ bem('items', ['hidden'], grid__base_class, ['js-grid-hidden']) }} aria-hidden="true">
        {% for item in items|slice(show_items) %}
          {{ item }}
        {% endfor %}
      </div>
    {% endif %}

  {% endblock %}

  {% if show_more and items|length > show_items %}
    <div {{ bem('controls', [], grid__base_class) }}>
      {% include "@mb/partials/button.twig" with {
        button__modifiers: ['primary'],
        button__extra_classes: ['js-grid'],
        button__content: button__content|default('Show all'|t),
      } %}
    </div>
  {% endif %}

</div>

