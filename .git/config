[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[remote "origin"]
	url = **************:guncha25/mb.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "master"]
	remote = origin
	merge = refs/heads/master
	vscode-merge-base = origin/master
[branch "feat-species-list"]
	remote = origin
	merge = refs/heads/feat-species-list
[branch "feat-google-places-api"]
	remote = origin
	merge = refs/heads/feat-google-places-api
[branch "time-diff-fix"]
	remote = origin
	merge = refs/heads/time-diff-fix
[pull]
	rebase = false
[branch "language-switcher"]
	remote = origin
	merge = refs/heads/language-switcher
[branch "develop"]
	remote = origin
	merge = refs/heads/develop
