uuid: 2d29fd30-3f49-454a-9824-c75c3d7f30cd
langcode: en
status: true
dependencies:
  config:
    - field.storage.user.field_display_name
  module:
    - user
id: user.user.field_display_name
field_name: field_display_name
entity_type: user
bundle: user
label: 'Display name'
description: 'Preferred name to be used when displaying account. This will only be used for display purposes if set.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
