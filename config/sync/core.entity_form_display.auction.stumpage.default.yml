uuid: a1bc809e-42e6-4f2b-978f-55a9d85afb29
langcode: en
status: true
dependencies:
  config:
    - auctions.auction_type.stumpage
    - field.field.auction.stumpage.field_360_images
    - field.field.auction.stumpage.field_administrative_area
    - field.field.auction.stumpage.field_agreement
    - field.field.auction.stumpage.field_agreement_signing
    - field.field.auction.stumpage.field_agreement_type
    - field.field.auction.stumpage.field_attachments
    - field.field.auction.stumpage.field_auction_type
    - field.field.auction.stumpage.field_bidding_method
    - field.field.auction.stumpage.field_cadastre_number
    - field.field.auction.stumpage.field_comments
    - field.field.auction.stumpage.field_custom_auction
    - field.field.auction.stumpage.field_cutting_area
    - field.field.auction.stumpage.field_cutting_type
    - field.field.auction.stumpage.field_delivery_conditions
    - field.field.auction.stumpage.field_extra_emails
    - field.field.auction.stumpage.field_fees
    - field.field.auction.stumpage.field_felling_certificate_files
    - field.field.auction.stumpage.field_felling_certificate_num
    - field.field.auction.stumpage.field_forest_volume
    - field.field.auction.stumpage.field_forwarding_distance
    - field.field.auction.stumpage.field_images
    - field.field.auction.stumpage.field_liz_score
    - field.field.auction.stumpage.field_map
    - field.field.auction.stumpage.field_map_image
    - field.field.auction.stumpage.field_measurements_not_made
    - field.field.auction.stumpage.field_notifications_sent
    - field.field.auction.stumpage.field_object
    - field.field.auction.stumpage.field_other_participants
    - field.field.auction.stumpage.field_owner_confirmation_file
    - field.field.auction.stumpage.field_ownership
    - field.field.auction.stumpage.field_participants
    - field.field.auction.stumpage.field_previous_price
    - field.field.auction.stumpage.field_price_group
    - field.field.auction.stumpage.field_privacy
    - field.field.auction.stumpage.field_private
    - field.field.auction.stumpage.field_property_area_size
    - field.field.auction.stumpage.field_property_auction
    - field.field.auction.stumpage.field_property_forest_condition
    - field.field.auction.stumpage.field_property_forest_size
    - field.field.auction.stumpage.field_property_name
    - field.field.auction.stumpage.field_purchase_agreement
    - field.field.auction.stumpage.field_quantity
    - field.field.auction.stumpage.field_route_and_strorage
    - field.field.auction.stumpage.field_species_composition
    - field.field.auction.stumpage.field_species_list
    - field.field.auction.stumpage.field_step
    - field.field.auction.stumpage.field_stumpage_auction
    - field.field.auction.stumpage.field_total_forest_stock
    - field.field.auction.stumpage.field_video
    - field.field.auction.stumpage.field_view_count
  module:
    - allowed_text_format_field_widget
    - cshs
    - datetime_flatpickr
    - datetimehideseconds
    - field_group
    - file
    - image
    - inline_entity_form
    - leaflet
    - paragraphs_asymmetric_translation_widgets
    - text
third_party_settings:
  field_group:
    group_stumpage_information:
      children:
        - field_forest_volume
        - field_cutting_area
        - field_forwarding_distance
        - field_route_and_strorage
        - field_delivery_conditions
        - field_cutting_type
        - field_measurements_not_made
        - field_species_composition
        - field_price_group
      label: 'Felling information'
      region: content
      parent_name: ''
      weight: 6
      format_type: html_element
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: true
        label_element: h2
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
    group_location:
      children:
        - field_cadastre_number
        - field_property_name
        - field_administrative_area
      label: Location
      region: content
      parent_name: ''
      weight: 1
      format_type: html_element
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: true
        label_element: h2
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
    group_attachments:
      children:
        - field_attachments
        - field_images
        - field_video
      label: Attachments
      region: content
      parent_name: ''
      weight: 11
      format_type: html_element
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: true
        label_element: h2
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
    group_auction_information:
      children:
        - field_bidding_method
        - field_step
        - start_price
        - end_time
      label: 'Auction information'
      region: content
      parent_name: ''
      weight: 8
      format_type: html_element
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: true
        label_element: h2
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
    group_extra_information:
      children:
        - description
        - field_extra_emails
      label: 'Extra information'
      region: content
      parent_name: ''
      weight: 10
      format_type: html_element
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: true
        label_element: h2
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
    group_felling_certificate:
      children:
        - field_felling_certificate_num
        - field_felling_certificate_files
        - field_ownership
        - field_owner_confirmation_file
      label: 'Felling certificate'
      region: content
      parent_name: ''
      weight: 2
      format_type: html_element
      format_settings:
        classes: ''
        show_empty_fields: false
        id: felling-certificate-tooltip
        label_as_html: false
        element: div
        show_label: true
        label_element: h2
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
    group_species_list:
      children:
        - field_species_list
      label: 'Species composition'
      region: content
      parent_name: ''
      weight: 7
      format_type: html_element
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: true
        label_element: h2
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
    group_property_info:
      children:
        - field_property_area_size
        - field_property_forest_size
        - field_total_forest_stock
        - field_property_forest_condition
      label: 'Property information'
      region: content
      parent_name: ''
      weight: 5
      format_type: html_element
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: true
        label_element: h2
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
    group_property_auction:
      children:
        - field_auction_type
        - field_private
        - group_private_options
      label: 'Auction type'
      region: content
      parent_name: ''
      weight: 0
      format_type: html_element
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: true
        label_element: h2
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
    group_private_options:
      children:
        - field_participants
        - field_other_participants
      label: 'Private options'
      region: content
      parent_name: group_property_auction
      weight: 48
      format_type: html_element
      format_settings:
        classes: no-card
        show_empty_fields: false
        id: ''
        element: div
        show_label: false
        label_element: h3
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
    group_purchase_agreement:
      children:
        - field_agreement_type
        - field_purchase_agreement
        - field_agreement_signing
      label: 'Purchase agreement'
      region: content
      parent_name: ''
      weight: 12
      format_type: html_element
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: true
        label_element: h2
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
    group_comments:
      children:
        - field_comments
        - field_fees
        - field_privacy
        - uid
      label: Meta
      region: content
      parent_name: ''
      weight: 13
      format_type: details_sidebar
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
        weight: 0
    group_auction_object:
      children:
        - field_object
        - field_quantity
        - field_liz_score
      label: 'Auction object'
      region: content
      parent_name: ''
      weight: 3
      format_type: html_element
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: true
        label_element: h2
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
id: auction.stumpage.default
targetEntityType: auction
bundle: stumpage
mode: default
content:
  administrative_area:
    type: entity_reference_autocomplete
    weight: 4
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  description:
    type: allowed_text_format_field_widget
    weight: 5
    region: content
    settings:
      rows: '5'
      placeholder: ''
      allowed_format:
        basic: basic
        email_html: 0
        full: 0
        plain_text: 0
    third_party_settings: {  }
  end_time:
    type: datetime_flatpickr
    weight: 7
    region: content
    settings:
      dateFormat: 'Y-m-d H:i'
      use_system_format: false
      system_date_format: ''
      altFormat: 'd.m.Y H:i'
      altInput: true
      allowInput: false
      defaultDate: ''
      defaultHour: ''
      defaultMinute: ''
      disabledWeekDays: {  }
      enableTime: true
      enableSeconds: false
      inline: false
      maxDate: ''
      minDate: ''
      maxTime:
        hour: ''
        min: ''
      minTime:
        hour: ''
        min: ''
      minuteIncrement: 15
      position: auto
      time_24hr: true
      weekNumbers: true
    third_party_settings:
      datetimehideseconds:
        hide: true
  field_administrative_area:
    type: cshs
    weight: 8
    region: content
    settings:
      save_lineage: false
      force_deepest: false
      parent: null
      level_labels: ''
      hierarchy_depth: 0
      required_depth: 0
      none_label: '- Please select -'
    third_party_settings: {  }
  field_agreement_signing:
    type: options_buttons
    weight: 42
    region: content
    settings: {  }
    third_party_settings: {  }
  field_agreement_type:
    type: options_buttons
    weight: 39
    region: content
    settings: {  }
    third_party_settings: {  }
  field_attachments:
    type: file_generic
    weight: 28
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  field_auction_type:
    type: options_buttons
    weight: 43
    region: content
    settings: {  }
    third_party_settings: {  }
  field_bidding_method:
    type: options_select
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cadastre_number:
    type: string_textfield
    weight: 6
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_comments:
    type: text_textarea
    weight: 32
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_cutting_area:
    type: number
    weight: 20
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cutting_type:
    type: options_buttons
    weight: 24
    region: content
    settings: {  }
    third_party_settings: {  }
  field_delivery_conditions:
    type: options_select
    weight: 23
    region: content
    settings: {  }
    third_party_settings: {  }
  field_extra_emails:
    type: email_default
    weight: 6
    region: content
    settings:
      placeholder: ''
      size: 60
    third_party_settings: {  }
  field_fees:
    type: options_buttons
    weight: 33
    region: content
    settings: {  }
    third_party_settings: {  }
  field_felling_certificate_files:
    type: file_generic
    weight: 8
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  field_felling_certificate_num:
    type: string_textfield
    weight: 7
    region: content
    settings:
      size: 12
      placeholder: ''
    third_party_settings: {  }
  field_forest_volume:
    type: number
    weight: 19
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_forwarding_distance:
    type: number
    weight: 21
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_images:
    type: image_image
    weight: 29
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: ''
    third_party_settings: {  }
  field_liz_score:
    type: number
    weight: 16
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_map:
    type: leaflet_widget_default
    weight: 9
    region: content
    settings:
      geometry_validation: false
      map:
        leaflet_map: 'Auction widget'
        height: 400
        auto_center: 1
        map_position:
          force: false
          center:
            lat: 56.9435
            lon: 24.0704
          zoomControlPosition: topleft
          zoom: 7
          minZoom: 1
          maxZoom: 18
          zoomFiner: 0
        scroll_zoom_enabled: 1
      input:
        show: true
        readonly: false
      toolbar:
        position: topright
        marker: defaultMarker
        drawPolyline: true
        drawRectangle: false
        drawPolygon: true
        drawCircle: false
        drawText: false
        editMode: true
        dragMode: true
        cutPolygon: false
        removalMode: true
        rotateMode: false
      reset_map:
        control: false
        options: '{"position":"topleft","title":"Reset View"}'
      map_scale:
        control: false
        options: '{"position":"bottomright","maxWidth":100,"metric":true,"imperial":false,"updateWhenIdle":false}'
      fullscreen:
        control: true
        options: '{"position":"topleft","pseudoFullscreen":false}'
      path: '{"color":"#3388ff","opacity":"1.0","stroke":true,"weight":3,"fill":"depends","fillColor":"*","fillOpacity":"0.0"}'
      feature_properties:
        values: ''
      locate:
        control: false
        options: '{"position":"topright","setView":"untilPanOrZoom","returnToPrevBounds":true,"keepCurrentZoomLevel":true,"strings":{"title":"Locate my position"}}'
        automatic: false
      geocoder:
        control: false
        settings:
          popup: false
          position: topright
          input_size: 20
          providers: {  }
          min_terms: 4
          delay: 800
          zoom: 16
          options: ''
    third_party_settings: {  }
  field_measurements_not_made:
    type: options_buttons
    weight: 25
    region: content
    settings: {  }
    third_party_settings: {  }
  field_object:
    type: options_select
    weight: 14
    region: content
    settings: {  }
    third_party_settings: {  }
  field_other_participants:
    type: string_textfield
    weight: 8
    region: content
    settings:
      size: 60
      placeholder: 'Uzņēmums vai persona, telefons un/vai e-pasts.'
    third_party_settings: {  }
  field_owner_confirmation_file:
    type: file_generic
    weight: 10
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  field_ownership:
    type: options_buttons
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  field_participants:
    type: options_buttons
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  field_price_group:
    type: options_select
    weight: 27
    region: content
    settings: {  }
    third_party_settings: {  }
  field_privacy:
    type: options_buttons
    weight: 34
    region: content
    settings: {  }
    third_party_settings: {  }
  field_private:
    type: boolean_checkbox
    weight: 47
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_property_area_size:
    type: number
    weight: 9
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_property_forest_condition:
    type: options_select
    weight: 12
    region: content
    settings: {  }
    third_party_settings: {  }
  field_property_forest_size:
    type: number
    weight: 10
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_property_name:
    type: string_textfield
    weight: 7
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_purchase_agreement:
    type: file_generic
    weight: 41
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  field_quantity:
    type: number
    weight: 15
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_route_and_strorage:
    type: options_select
    weight: 22
    region: content
    settings: {  }
    third_party_settings: {  }
  field_species_composition:
    type: string_textfield
    weight: 26
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_species_list:
    type: paragraphs_classic_asymmetric
    weight: 5
    region: content
    settings:
      title: Suga
      title_plural: Suga
      edit_mode: open
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: species
    third_party_settings: {  }
  field_step:
    type: options_select
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  field_total_forest_stock:
    type: number
    weight: 11
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_video:
    type: inline_entity_form_complex
    weight: 31
    region: content
    settings:
      form_mode: default
      override_labels: false
      label_singular: ''
      label_plural: ''
      allow_new: true
      allow_existing: false
      match_operator: CONTAINS
      allow_duplicate: false
      collapsible: false
      collapsed: false
      revision: false
      removed_reference: delete
    third_party_settings: {  }
  price:
    type: number
    weight: 2
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  start_price:
    type: number
    weight: 6
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  start_time:
    type: datetime_default
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 35
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  created: true
  currency: true
  field_360_images: true
  field_agreement: true
  field_custom_auction: true
  field_map_image: true
  field_notifications_sent: true
  field_previous_price: true
  field_property_auction: true
  field_stumpage_auction: true
  field_view_count: true
  langcode: true
  status: true
  translation: true
