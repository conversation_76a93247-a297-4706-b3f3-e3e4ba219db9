uuid: 3e8fa474-56a4-441f-8d13-ec2a634edca9
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_image
    - paragraphs.paragraphs_type.image
  module:
    - content_translation
    - image
third_party_settings:
  content_translation:
    translation_sync:
      file: file
      alt: '0'
      title: '0'
id: paragraph.image.field_image
field_name: field_image
entity_type: paragraph
bundle: image
label: Image
description: ''
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: 'image-paragraph/[date:custom:Y]-[date:custom:m]'
  file_extensions: 'png gif jpg jpeg'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: true
  alt_field_required: true
  title_field: false
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
