uuid: ce028b70-4f31-45bb-8d01-44f0078ede1b
langcode: en
status: true
dependencies:
  config:
    - core.entity_form_mode.user.register
    - field.field.user.user.commerce_remote_id
    - field.field.user.user.field_auction_notifications
    - field.field.user.user.field_bid_notifications
    - field.field.user.user.field_block_users
    - field.field.user.user.field_display_name
    - field.field.user.user.field_domain
    - field.field.user.user.field_domain_admin
    - field.field.user.user.field_extra_maps
    - field.field.user.user.field_from
    - field.field.user.user.field_full_name
    - field.field.user.user.field_interests
    - field.field.user.user.field_legal_entity
    - field.field.user.user.field_locations
    - field.field.user.user.field_phone
    - field.field.user.user.field_purchase_agreement
    - field.field.user.user.field_rating
    - field.field.user.user.field_regions_of_interest
    - field.field.user.user.field_registration_number
    - field.field.user.user.field_status
  module:
    - telephone
    - user
id: user.user.register
targetEntityType: user
bundle: user
mode: register
content:
  account:
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  field_from:
    type: options_select
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  field_full_name:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_interests:
    type: options_select
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  field_legal_entity:
    type: options_select
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  field_phone:
    type: telephone_default
    weight: 5
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_registration_number:
    type: number
    weight: 1
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  google_analytics:
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  commerce_remote_id: true
  customer_profiles: true
  field_auction_notifications: true
  field_bid_notifications: true
  field_block_users: true
  field_display_name: true
  field_domain: true
  field_domain_admin: true
  field_extra_maps: true
  field_locations: true
  field_purchase_agreement: true
  field_rating: true
  field_regions_of_interest: true
  field_status: true
  langcode: true
  language: true
  path: true
  simplenews: true
  timezone: true
