uuid: 50b64f54-1b45-4740-a112-6d943e441c0d
langcode: en
status: true
dependencies: {  }
name: news_teaser_small
label: 'News teaser small'
effects:
  5f99bb20-232f-4ac3-a790-991c2e8cd9cc:
    uuid: 5f99bb20-232f-4ac3-a790-991c2e8cd9cc
    id: image_scale_and_crop
    weight: 1
    data:
      width: 420
      height: 200
      anchor: center-center
  b8438ad1-00fb-4a49-9e5f-074989200418:
    uuid: b8438ad1-00fb-4a49-9e5f-074989200418
    id: image_convert
    weight: 2
    data:
      extension: webp
pipeline: __default__
