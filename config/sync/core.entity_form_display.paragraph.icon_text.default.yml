uuid: 28ea4b58-51bf-4bb7-837d-f4ff41e80d0f
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.icon_text.field_content
    - field.field.paragraph.icon_text.field_image
    - field.field.paragraph.icon_text.field_lord_icon
    - image.style.thumbnail
    - paragraphs.paragraphs_type.icon_text
  module:
    - image
    - text
id: paragraph.icon_text.default
targetEntityType: paragraph
bundle: icon_text
mode: default
content:
  field_content:
    type: text_textarea
    weight: 3
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_image:
    type: image_image
    weight: 1
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_lord_icon:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  translation:
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  created: true
  status: true
