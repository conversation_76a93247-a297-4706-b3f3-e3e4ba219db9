uuid: c23fa23d-4a15-4055-b369-6c64ffcbc9aa
langcode: en
status: true
dependencies:
  config:
    - commerce_product.commerce_product_type.default
id: commerce_product.default.uid
field_name: uid
entity_type: commerce_product
bundle: default
label: Author
description: 'The product author.'
required: false
translatable: true
default_value: {  }
default_value_callback: 'Drupal\commerce_product\Entity\Product::getDefaultEntityOwner'
settings:
  handler: default
  handler_settings: {  }
field_type: entity_reference
