uuid: 84a9f491-8cde-4100-8067-4ad9ef96aa1e
langcode: en
status: true
dependencies:
  config:
    - field.field.simplenews_subscriber.simplenews_subscriber.field_regions_of_interest
  module:
    - shs
    - simplenews
_core:
  default_config_hash: 2Nvy8pq5vcEXAV16jh_aQ93MsG3CNQG45VcpztHiT7o
id: simplenews_subscriber.simplenews_subscriber.default
targetEntityType: simplenews_subscriber
bundle: simplenews_subscriber
mode: default
content:
  field_regions_of_interest:
    type: options_shs
    weight: 1
    region: content
    settings:
      display_node_count: false
      create_new_items: false
      create_new_levels: false
      force_deepest: false
    third_party_settings: {  }
  mail:
    type: email_default
    weight: 0
    region: content
    settings:
      placeholder: ''
      size: 60
    third_party_settings: {  }
hidden:
  uid: true
