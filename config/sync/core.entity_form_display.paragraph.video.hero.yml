uuid: c34afd14-ec2a-45b1-8bd6-48b7817ad92b
langcode: en
status: true
dependencies:
  config:
    - core.entity_form_mode.paragraph.hero
    - field.field.paragraph.video.field_content
    - field.field.paragraph.video.field_image
    - field.field.paragraph.video.field_link
    - field.field.paragraph.video.field_video
    - image.style.thumbnail
    - paragraphs.paragraphs_type.video
  module:
    - file
    - image
    - text
id: paragraph.video.hero
targetEntityType: paragraph
bundle: video
mode: hero
content:
  field_content:
    type: text_textarea
    weight: 3
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_image:
    type: image_image
    weight: 2
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_video:
    type: file_generic
    weight: 1
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  translation:
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  created: true
  field_link: true
  status: true
