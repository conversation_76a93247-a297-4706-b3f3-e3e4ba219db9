uuid: bf7c710f-939d-4b99-a43a-ea2f4ebcc56a
langcode: en
status: true
dependencies:
  config:
    - field.storage.user.field_block_users
  module:
    - user
id: user.user.field_block_users
field_name: field_block_users
entity_type: user
bundle: user
label: 'Blocked users'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:user'
  handler_settings:
    target_bundles: null
    sort:
      field: _none
      direction: ASC
    auto_create: false
    filter:
      type: _none
    include_anonymous: false
field_type: entity_reference
