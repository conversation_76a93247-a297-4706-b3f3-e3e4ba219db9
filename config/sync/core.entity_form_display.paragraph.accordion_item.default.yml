uuid: 139aeaa3-db21-462e-8d13-4ef5ea98241f
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.accordion_item.field_color
    - field.field.paragraph.accordion_item.field_content
    - field.field.paragraph.accordion_item.field_image
    - field.field.paragraph.accordion_item.field_title
    - image.style.thumbnail
    - paragraphs.paragraphs_type.accordion_item
  module:
    - image
    - text
id: paragraph.accordion_item.default
targetEntityType: paragraph
bundle: accordion_item
mode: default
content:
  field_color:
    type: options_select
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
  field_content:
    type: text_textarea
    weight: 4
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_image:
    type: image_image
    weight: 2
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_title:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  translation:
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  created: true
  status: true
