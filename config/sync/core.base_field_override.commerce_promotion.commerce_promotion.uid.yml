uuid: 03157e05-3156-4412-bf2b-8c81059b927e
langcode: lv
status: true
dependencies:
  module:
    - commerce_promotion
id: commerce_promotion.commerce_promotion.uid
field_name: uid
entity_type: commerce_promotion
bundle: commerce_promotion
label: Īpašnieks
description: 'The promotion owner.'
required: false
translatable: false
default_value: {  }
default_value_callback: 'Drupal\commerce_promotion\Entity\Promotion::getDefaultEntityOwner'
settings:
  handler: default
  handler_settings: {  }
field_type: entity_reference
