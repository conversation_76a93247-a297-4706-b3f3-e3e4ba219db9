uuid: cf290dde-8e24-499d-b41e-20c580c5745d
langcode: en
status: true
dependencies:
  config:
    - commerce_order.commerce_order_type.default
    - core.entity_view_mode.commerce_order.user
    - field.field.commerce_order.default.field_auction
    - field.field.commerce_order.default.field_comment
  module:
    - commerce_order
    - entity_reference_revisions
    - options
_core:
  default_config_hash: XRgGhXKcDeqhvgwHROyCDmRx7wfApZmGqxYl_9fE7SU
id: commerce_order.default.user
targetEntityType: commerce_order
bundle: default
mode: user
content:
  billing_profile:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 0
    region: content
  completed:
    type: timestamp
    label: inline
    settings:
      date_format: short
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: ''
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 2
    region: content
  mail:
    type: basic_string
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  order_items:
    type: commerce_order_item_table
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 5
    region: content
  order_number:
    type: string
    label: inline
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 0
    region: content
  placed:
    type: timestamp
    label: inline
    settings:
      date_format: short
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: ''
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 3
    region: content
  state:
    type: list_default
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 4
    region: content
  total_price:
    type: commerce_order_total_summary
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 6
    region: content
hidden:
  balance: true
  changed: true
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
  field_auction: true
  field_comment: true
  ip_address: true
  store_id: true
  total_paid: true
  uid: true
