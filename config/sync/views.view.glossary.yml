uuid: 14298d88-3a5c-47ba-855d-cf999b3261a0
langcode: en
status: false
dependencies:
  config:
    - system.menu.main
  module:
    - node
    - user
_core:
  default_config_hash: Z1Lg95xnf11BTHIugkzple_pdxZdECiqLRcrLVo3bcw
id: glossary
label: Glossary
module: node
description: 'All content, by letter.'
tag: default
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: Virsraksts
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        name:
          id: name
          table: users_field_data
          field: name
          relationship: uid
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: name
          plugin_id: field
          label: Autors
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: user_name
        changed:
          id: changed
          table: node_field_data
          field: changed
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: changed
          plugin_id: field
          label: 'Pēdējais papildinājums'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: timestamp
          settings:
            date_format: long
            custom_date_format: ''
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
      pager:
        type: mini
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 36
          total_pages: 0
          id: 0
          tags:
            next: '"'
            previous: '"'
          expose:
            items_per_page: false
            items_per_page_label: 'Vienumi vienā lapā'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- Visi -'
            offset: false
            offset_label: Ofsets
      exposed_form:
        type: basic
        options:
          submit_button: Apstiprināt
          reset_button: false
          reset_button_label: Attīrīt
          exposed_sorts_label: 'Kārtot pēc'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Dilst.
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts: {  }
      arguments:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: string
          default_action: default
          exception:
            title_enable: true
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: a
          summary_options: {  }
          summary:
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          glossary: true
          limit: 1
          case: upper
          path_case: lower
          transform_dash: false
          break_phrase: false
      filters:
        langcode:
          id: langcode
          table: node_field_data
          field: langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: language
          operator: in
          value:
            '***LANGUAGE_language_content***': '***LANGUAGE_language_content***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
          columns:
            title: title
            name: name
            changed: changed
          default: title
          info:
            title:
              sortable: true
              separator: ''
            name:
              sortable: true
              separator: ''
            changed:
              sortable: true
              separator: ''
          override: true
          sticky: false
          summary: ''
          order: asc
          empty_table: false
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        uid:
          id: uid
          table: node_field_data
          field: uid
          relationship: none
          group_type: group
          admin_label: author
          plugin_id: standard
          required: false
      use_ajax: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  attachment_1:
    id: attachment_1
    display_title: Pielikums
    display_plugin: attachment
    position: 2
    display_options:
      pager:
        type: none
        options:
          offset: 0
          items_per_page: 0
      arguments:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: string
          default_action: summary
          exception:
            title_enable: true
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: a
          summary_options:
            items_per_page: 25
            inline: true
            separator: ' | '
          summary:
            format: unformatted_summary
          specify_validation: true
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          glossary: true
          limit: 1
          case: upper
          path_case: lower
          transform_dash: false
          break_phrase: false
      query:
        type: views_query
        options: {  }
      defaults:
        arguments: false
      display_extenders: {  }
      displays:
        default: default
        page_1: page_1
      inherit_arguments: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  page_1:
    id: page_1
    display_title: Lapa
    display_plugin: page
    position: 1
    display_options:
      query:
        type: views_query
        options: {  }
      display_extenders: {  }
      path: glossary
      menu:
        type: normal
        title: Glossary
        weight: 0
        menu_name: main
        parent: ''
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
