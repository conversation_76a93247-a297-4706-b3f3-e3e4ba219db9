uuid: a88490ce-a11d-4907-9ada-0697c0d9498f
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.banner.field_background
    - field.field.paragraph.banner.field_blocks
    - field.field.paragraph.banner.field_layout
    - field.field.paragraph.banner.field_width
    - paragraphs.paragraphs_type.banner
  module:
    - paragraphs_asymmetric_translation_widgets
id: paragraph.banner.default
targetEntityType: paragraph
bundle: banner
mode: default
content:
  field_background:
    type: options_select
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  field_blocks:
    type: paragraphs_classic_asymmetric
    weight: 4
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      add_mode: button
      form_display_mode: default
      default_paragraph_type: _none
    third_party_settings: {  }
  field_layout:
    type: options_select
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
  field_width:
    type: options_select
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  translation:
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  created: true
  status: true
