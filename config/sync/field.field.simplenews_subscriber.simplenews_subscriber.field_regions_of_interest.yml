uuid: 67357a25-094b-4d13-86b4-13a7bf76df63
langcode: en
status: true
dependencies:
  config:
    - field.storage.simplenews_subscriber.field_regions_of_interest
    - taxonomy.vocabulary.regions
  module:
    - simplenews
id: simplenews_subscriber.simplenews_subscriber.field_regions_of_interest
field_name: field_regions_of_interest
entity_type: simplenews_subscriber
bundle: simplenews_subscriber
label: 'Regions of interest'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      regions: regions
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: administrative_areas
field_type: entity_reference
