uuid: 960df2d5-4a3a-42cc-b0f5-d8ceefbae3ef
langcode: en
status: true
dependencies:
  module:
    - better_exposed_filters
    - commerce_invoice
    - commerce_price
    - options
    - state_machine
    - user
id: commerce_user_invoices
label: 'User invoices'
module: views
description: 'Display a list of invoices for a user.'
tag: Commerce
base_table: commerce_invoice_field_data
base_field: invoice_id
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: Invoices
      fields:
        invoice_id:
          id: invoice_id
          table: commerce_invoice_field_data
          field: invoice_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_invoice
          entity_field: invoice_id
          plugin_id: field
          label: ID
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        invoice_number:
          id: invoice_number
          table: commerce_invoice_field_data
          field: invoice_number
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: invoice_number
          plugin_id: field
          label: 'Invoice number'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        invoice_date:
          id: invoice_date
          table: commerce_invoice_field_data
          field: invoice_date
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_invoice
          entity_field: invoice_date
          plugin_id: field
          label: Date
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: short
            custom_date_format: ''
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        total_price__number:
          id: total_price__number
          table: commerce_invoice_field_data
          field: total_price__number
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_invoice
          entity_field: total_price
          plugin_id: field
          label: Total
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: number
          type: commerce_price_default
          settings:
            strip_trailing_zeroes: false
            currency_display: symbol
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        state:
          id: state
          table: commerce_invoice_field_data
          field: state
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_invoice
          entity_field: state
          plugin_id: field
          label: State
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: list_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        nothing:
          id: nothing
          table: views
          field: nothing
          relationship: none
          group_type: group
          admin_label: PDF
          plugin_id: custom
          label: Lejupielādēt
          exclude: false
          alter:
            alter_text: true
            text: PDF
            make_link: true
            path: '/invoice/{{ invoice_id }}/download'
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: 'Skatīt un lejupielādēt rēķinu'
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
      pager:
        type: full
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 25
          total_pages: null
          id: 0
          tags:
            next: 'Next ›'
            previous: '‹ Previous'
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'view own commerce_invoice'
      cache:
        type: tag
        options: {  }
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: 'No invoices yet.'
            format: basic
          tokenize: false
      sorts:
        invoice_date:
          id: invoice_date
          table: commerce_invoice_field_data
          field: invoice_date
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_invoice
          entity_field: invoice_date
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: invoice_date
          exposed: false
          granularity: second
      arguments:
        uid:
          id: uid
          table: commerce_invoice_field_data
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_invoice
          entity_field: uid
          plugin_id: entity_target_id
          default_action: 'not found'
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: current_user
          default_argument_options: {  }
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: commerce_current_user
            fail: 'not found'
          validate_options:
            admin_permission: 'view commerce_invoice'
          break_phrase: false
          not: false
          target_entity_type_id: user
      filters:
        state:
          id: state
          table: commerce_invoice_field_data
          field: state
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_invoice
          entity_field: state
          plugin_id: state_machine_state
          operator: 'not in'
          value:
            draft: draft
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        default_langcode:
          id: default_langcode
          table: commerce_invoice_field_data
          field: default_langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_invoice
          entity_field: default_langcode
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            invoice_id: invoice_id
            invoice_number: invoice_number
            invoice_date: invoice_date
            total_price__number: total_price__number
            state: state
            nothing: nothing
          default: invoice_date
          info:
            invoice_id:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            invoice_number:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            invoice_date:
              sortable: true
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-low
            total_price__number:
              sortable: true
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            state:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            nothing:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: true
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      use_ajax: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user
        - user.permissions
      tags: {  }
  invoice_page:
    id: invoice_page
    display_title: 'User invoices'
    display_plugin: page
    position: 1
    display_options:
      exposed_form:
        type: bef
        options:
          submit_button: Apply
          reset_button: true
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic
          bef:
            general:
              autosubmit: true
              autosubmit_exclude_textfield: false
              autosubmit_textfield_delay: 500
              autosubmit_hide: true
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              secondary_open: false
              autosubmit_textfield_minimum_length: 3
            filter:
              state_1:
                plugin_id: bef
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: false
              invoice_date:
                plugin_id: default
                advanced:
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
              invoice_date_1:
                plugin_id: default
                advanced:
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
      filters:
        state:
          id: state
          table: commerce_invoice_field_data
          field: state
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_invoice
          entity_field: state
          plugin_id: state_machine_state
          operator: 'not in'
          value:
            draft: draft
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        default_langcode:
          id: default_langcode
          table: commerce_invoice_field_data
          field: default_langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_invoice
          entity_field: default_langcode
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        state_1:
          id: state_1
          table: commerce_invoice_field_data
          field: state
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_invoice
          entity_field: state
          plugin_id: state_machine_state
          operator: in
          value:
            pending: pending
            paid: paid
            canceled: canceled
          group: 1
          exposed: true
          expose:
            operator_id: state_1_op
            label: Statuss
            description: ''
            use_operator: false
            operator: state_1_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: state_1
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
            reduce: true
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        exposed_form: false
        filters: false
        filter_groups: false
        header: false
      header: {  }
      rendering_language: '***LANGUAGE_entity_translation***'
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
        ajax_history:
          enable_history: false
      path: user/%user/invoices
      menu:
        type: tab
        title: Rēķini
        description: ''
        weight: 0
        expanded: false
        menu_name: account
        parent: ''
        context: '0'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user
        - user.permissions
      tags: {  }
