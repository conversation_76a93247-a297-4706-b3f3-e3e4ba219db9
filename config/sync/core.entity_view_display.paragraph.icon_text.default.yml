uuid: f54e6100-40fd-46d0-87bc-963324bff3ec
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.icon_text.field_content
    - field.field.paragraph.icon_text.field_image
    - field.field.paragraph.icon_text.field_lord_icon
    - image.style.icon
    - paragraphs.paragraphs_type.icon_text
  module:
    - image
    - text
id: paragraph.icon_text.default
targetEntityType: paragraph
bundle: icon_text
mode: default
content:
  field_content:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  field_image:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: icon
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
  field_lord_icon: true
