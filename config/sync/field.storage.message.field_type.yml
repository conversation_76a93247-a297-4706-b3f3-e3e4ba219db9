uuid: b2977e03-3626-4fc9-8d6d-1b56c06a82b4
langcode: en
status: true
dependencies:
  module:
    - message
    - options
id: message.field_type
field_name: field_type
entity_type: message
type: list_string
settings:
  allowed_values:
    -
      value: registration_reminder
      label: 'registration reminder'
    -
      value: account_reminder
      label: 'account reminder'
    -
      value: single_email
      label: 'single email'
    -
      value: warning
      label: 'payment warning'
  allowed_values_function: ''
module: options
locked: false
cardinality: 1
translatable: true
indexes: {  }
persist_with_no_fields: false
custom_storage: false
