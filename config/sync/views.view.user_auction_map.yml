uuid: 46de3959-91f5-4569-a473-48597a38c38a
langcode: en
status: true
dependencies:
  config:
    - field.storage.auction.field_map
  module:
    - auctions
    - datetime
    - geofield
id: user_auction_map
label: 'User auction map'
module: views
description: 'Provides users auctions in map'
tag: ''
base_table: auction_field_data
base_field: id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        id:
          id: id
          table: auction_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: id
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_map:
          id: field_map
          table: auction__field_map
          field: field_map
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: geofield_default
          settings:
            output_format: json
            output_escape: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title:
          id: title
          table: auction
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: auction
          plugin_id: auction_title
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
      pager:
        type: none
        options:
          offset: 0
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: none
        options: {  }
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        end_time:
          id: end_time
          table: auction_field_data
          field: end_time
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: auction
          entity_field: end_time
          plugin_id: datetime
          order: ASC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
          granularity: second
      arguments:
        uid:
          id: uid
          table: bid
          field: uid
          relationship: reverse__bid__auction
          group_type: group
          admin_label: ''
          entity_type: bid
          entity_field: uid
          plugin_id: entity_target_id
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: user_id
          default_argument_options:
            user: 0
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
      filters:
        status:
          id: status
          table: auction_field_data
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: auction
          entity_field: status
          plugin_id: auctions_status
          operator: or
          value:
            finished: finished
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
        end_time:
          id: end_time
          table: auction_field_data
          field: end_time
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: auction
          entity_field: end_time
          plugin_id: datetime
          operator: '>='
          value:
            min: ''
            max: ''
            value: '2024-01-01 00:00:01'
            type: date
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        end_time_1:
          id: end_time_1
          table: auction_field_data
          field: end_time
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: auction
          entity_field: end_time
          plugin_id: datetime
          operator: '<='
          value:
            min: ''
            max: ''
            value: '2025-01-01 00:00:01'
            type: date
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups: {  }
      style:
        type: auctions_leaflet_map
        options:
          grouping: {  }
          data_source:
            field_map: field_map
          entity_source: __base_table
          leaflet_tooltip:
            value: ''
            options: '{"permanent":false,"direction":"center"}'
          name_field: ''
          leaflet_popup:
            value: '#rendered_entity_ajax'
            view_mode: map_teaser
            options: '{"maxWidth":"300","minWidth":"50","autoPan":true}'
          leaflet_map: Auction
          height: '550'
          height_unit: px
          hide_empty_map: 0
          gesture_handling: 1
          disable_wheel: 0
          fitbounds_options: '{"padding":[0,0]}'
          reset_map:
            control: 0
            options: '{"position":"topleft","title":"Reset View"}'
          map_scale:
            control: 0
            options: '{"position":"bottomright","maxWidth":100,"metric":true,"imperial":false,"updateWhenIdle":false}'
          map_position:
            force: 1
            center:
              lat: '56.9'
              lon: '24.8'
            zoomControlPosition: topleft
            zoom: '7'
            minZoom: '0'
            maxZoom: '16'
            zoomFiner: '0'
          weight: ''
          icon:
            iconType: marker
            iconUrl: modules/custom/auctions/img/auction.png
            shadowUrl: ''
            className: ''
            html: '<div></div>'
            html_class: leaflet-map-divicon
            circle_marker_options: '{"radius":100,"color":"red","fillColor":"#f03","fillOpacity":0.5}'
            iconSize:
              x: ''
              'y': ''
            iconAnchor:
              x: ''
              'y': ''
            shadowSize:
              x: ''
              'y': ''
            shadowAnchor:
              x: ''
              'y': ''
            popupAnchor:
              x: ''
              'y': ''
          leaflet_markercluster:
            control: 0
            excluded: '0'
            options: '{"spiderfyOnMaxZoom":true,"showCoverageOnHover":true,"removeOutsideVisibleBounds":false}'
            include_path: 0
          fullscreen:
            control: 1
            options: '{"position":"topleft","pseudoFullscreen":false}'
          path: '{"color":"#3388ff","opacity":"1.0","stroke":true,"weight":3,"fill":"depends","fillColor":"*","fillOpacity":"0.2","radius":"6"}'
          feature_properties:
            values: ''
          locate:
            control: 0
            options: '{"position":"topright","setView":"untilPanOrZoom","returnToPrevBounds":true,"keepCurrentZoomLevel":true,"strings":{"title":"Locate my position"}}'
            automatic: 0
          map_lazy_load:
            lazy_load: 1
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        reverse__bid__auction:
          id: reverse__bid__auction
          table: auction_field_data
          field: reverse__bid__auction
          relationship: none
          group_type: group
          admin_label: Bid
          entity_type: auction
          plugin_id: standard
          required: true
      use_ajax: true
      header: {  }
      footer: {  }
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
        ajax_history: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
      tags:
        - 'config:field.storage.auction.field_map'
  user_auctions_sold_embed:
    id: user_auctions_sold_embed
    display_title: 'Seller auctions'
    display_plugin: embed
    position: 2
    display_options:
      arguments:
        uid:
          id: uid
          table: auction_field_data
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: auction
          entity_field: uid
          plugin_id: entity_target_id
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: user_id
          default_argument_options:
            user: 0
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
      defaults:
        relationships: false
        arguments: false
      relationships: {  }
      display_description: ''
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
        ajax_history: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
      tags:
        - 'config:field.storage.auction.field_map'
