uuid: aa8d92a9-7923-46d4-968d-fc1333e996f4
langcode: en
status: true
dependencies:
  config:
    - field.field.node.auction_offers.field_auction_offer
    - field.field.node.auction_offers.field_auction_reference
    - field.field.node.auction_offers.field_user_reference
    - node.type.auction_offers
  module:
    - user
id: node.auction_offers.default
targetEntityType: node
bundle: auction_offers
mode: default
content:
  field_auction_offer:
    type: number_decimal
    label: above
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 103
    region: content
  field_auction_reference:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 101
    region: content
  field_user_reference:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 102
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
  langcode: true
