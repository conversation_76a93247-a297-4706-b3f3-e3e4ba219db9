uuid: e3b19b03-c6a6-40e4-8f71-2c333b7c2a44
langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.field_additional_email
    - profile.type.customer
id: profile.customer.field_additional_email
field_name: field_additional_email
entity_type: profile
bundle: customer
label: 'Additional email'
description: 'Provide additional email where invoice will be sent.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: email
