diff --git a/core/modules/file/src/Plugin/Field/FieldType/FileFieldItemList.php b/core/modules/file/src/Plugin/Field/FieldType/FileFieldItemList.php
index ab936177bb..d58c610ae8 100644
--- a/core/modules/file/src/Plugin/Field/FieldType/FileFieldItemList.php
+++ b/core/modules/file/src/Plugin/Field/FieldType/FileFieldItemList.php
@@ -51,7 +51,7 @@ public function postSave($update) {
       $original_ids = [];
       $langcode = $this->getLangcode();
       $original = $entity->original;
-      if ($original->hasTranslation($langcode)) {
+      if ($original?->hasTranslation($langcode)) {
         $original_items = $original->getTranslation($langcode)->{$field_name};
         foreach ($original_items as $item) {
           $original_ids[] = $item->target_id;
