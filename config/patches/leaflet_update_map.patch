--- web/modules/contrib/leaflet/js/leaflet.widget.js.bak	2025-05-25 07:17:15
+++ web/modules/contrib/leaflet/js/leaflet.widget.js	2025-05-25 07:17:32
@@ -14,20 +14,18 @@
           return;
         }
 
-        // Define the leaflet-map-widget elements.
-        const leaflet_elements = $(once('behaviour-leaflet-widget', '#' + map_id));
-        leaflet_elements.each(function () {
-          // For each element define a new Drupal.Leaflet_Widget,
-          // if not already defined.
+        $('#' + map_id, context).each(function () {
           const map_container = $(this);
-          if (map_container.data('leaflet_widget') === undefined && leaflet_settings.lMap) {
-            const lMap = leaflet_settings.lMap;
+          // If the attached context contains any leaflet maps with widgets, make sure we have a
+          // Drupal.Leaflet_Widget object.
+          if (map_container.data('leaflet_widget') === undefined) {
+            const lMap = drupalSettings.leaflet[map_id].lMap;
             map_container.data('leaflet_widget', new Drupal.Leaflet_Widget(map_container, lMap, leaflet_settings));
             // Define the global Drupal.Leaflet[mapid] object to be accessible
             // from outside.
             Drupal.Leaflet_Widget[map_id] = map_container.data('leaflet_widget');
           }
-          else {
+          else if (map_container.data('leaflet_widget')) {
             // If we already had a widget, update map to make sure that WKT and map are synchronized.
             map_container.data('leaflet_widget').update_leaflet_widget_map();
             map_container.data('leaflet_widget').update_input_state();
